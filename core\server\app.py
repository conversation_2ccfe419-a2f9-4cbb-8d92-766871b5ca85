from contextlib import asynccontextmanager
from typing import List
from fastapi import APIRouter, FastAPI
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException
from core.services.database import db_manager
from core.config.app_logger import logger
from core.factory.agent_factory import AgentFactory
from core.auth.adapter_factory import AuthAdapterFactory
from core.services.model_config_service import ModelConfigService

# 应用启动入口，创建 FastAPI 实例并注册路由
# <AUTHOR> xiangjh
def create_app(routers: List[APIRouter]) -> FastAPI:

    @asynccontextmanager
    async def lifespan(app: FastAPI):
        logger.info("应用启动中... ")
        logger.info("PostgreSQL 初始化")
        await db_manager.init_db()
        logger.info("PostgreSQL 初始化完成")

        logger.info("Agents 初始化")
        await AgentFactory.load_agents()
        logger.info("Agents 初始化完成")

        logger.info("Agent 模型配置缓存初始化")
        await ModelConfigService.initialize()
        logger.info("Agent 模型配置缓存初始化完成")

        # 注册并启用自定义适配器
        AuthAdapterFactory.initialize()

        yield  # 应用运行期间

        logger.info("应用关闭中... 释放资源... ")
        # DatabaseManager doesn't have an explicit close method based on the provided code
        # The engine's connection pool will be disposed when the application exits
        logger.info("应用关闭完成")

    app = FastAPI(lifespan=lifespan)
    for router in routers:
        app.include_router(router, prefix="/api")

    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request, exc):
        return JSONResponse(
            status_code=exc.status_code,
            content={"code": exc.status_code, "message": exc.detail},
        )

    return app


async def run_server(host: str = "0.0.0.0", port: int = 8000, config_path: str = "config/application.yml", reload: bool = False):
    """简化的服务器启动函数

    Args:
        host (str): 绑定的主机地址
        port (int): 绑定的端口
        config_path (str): 配置文件路径
        reload (bool): 是否启用自动重载
    """
    import os
    import uvicorn

    # 设置配置文件路径
    os.environ["CONFIG_PATH"] = config_path

    # 创建应用
    app = create_app()

    # 启动服务器
    config = uvicorn.Config(
        app=app,
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

    server = uvicorn.Server(config)
    await server.serve()
