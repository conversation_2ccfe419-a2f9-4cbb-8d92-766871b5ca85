#!/usr/bin/env python3
"""
测试AgentFactory改造后的用户级别数据隔离功能
验证每次调用get_sender都会创建新的实例
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.factory.agent_factory import AgentFactory
from core.base.langchain_sender import LangchainBaseSender
from core.base.langgraph_sender import LangGraphBaseSender
from core.config.app_logger import logger
# 注册测试类
# @Author: xiangjh
class MockAgent:
    """模拟Agent对象"""
    def __init__(self, agent_code, target_type, recursion_limit=None):
        self.agent_code = agent_code
        self.target_type = target_type
        self.recursion_limit = recursion_limit

class TestSender(LangchainBaseSender):
    """测试用的Sender类"""
    def __init__(self, agent_code: str):
        super().__init__(agent_code)
        self.instance_id = id(self)
        print(f"创建TestSender实例: {agent_code}, 实例ID: {self.instance_id}")

class TestLangGraphSender(LangGraphBaseSender):
    """测试用的LangGraphSender类"""
    def __init__(self, agent_code: str, recursion_limit: int = 10):
        super().__init__(agent_code, recursion_limit)
        self.instance_id = id(self)
        print(f"创建TestLangGraphSender实例: {agent_code}, recursion_limit: {recursion_limit}, 实例ID: {self.instance_id}")

def test_instance_isolation():
    """测试实例隔离功能"""
    print("=" * 60)
    print("测试AgentFactory用户级别数据隔离功能")
    print("=" * 60)
    
    # 清空现有缓存
    AgentFactory._sender_classes.clear()
    AgentFactory._agents.clear()
    
    # 注册测试用的sender类和agent
    test_agent_code = "test-agent"
    test_langgraph_agent_code = "test-langgraph-agent"
    
    # 注册普通sender
    AgentFactory.register_sender_class(test_agent_code, TestSender)
    AgentFactory._agents[test_agent_code] = MockAgent(test_agent_code, "LANGCHAIN")
    
    # 注册LangGraph sender
    AgentFactory.register_sender_class(test_langgraph_agent_code, TestLangGraphSender)
    AgentFactory._agents[test_langgraph_agent_code] = MockAgent(test_langgraph_agent_code, "LANGGRAPH", 15)
    
    print(f"已注册sender类: {list(AgentFactory._sender_classes.keys())}")
    print(f"已注册agent: {list(AgentFactory._agents.keys())}")
    print()
    
    # 测试1: 验证多次调用get_sender返回不同实例
    print("测试1: 验证多次调用get_sender返回不同实例")
    print("-" * 40)
    
    instances = []
    for i in range(3):
        sender = AgentFactory.get_sender(test_agent_code)
        instances.append(sender)
        print(f"第{i+1}次调用 - 实例ID: {id(sender)}, agent_code: {sender.agent_code}")
    
    # 验证所有实例都不相同
    unique_ids = set(id(instance) for instance in instances)
    print(f"创建了 {len(instances)} 个实例，唯一实例数: {len(unique_ids)}")
    assert len(unique_ids) == len(instances), "实例隔离失败：存在重复实例"
    print("✓ 测试1通过：每次调用都创建了新实例")
    print()
    
    # 测试2: 验证LangGraph sender的实例隔离
    print("测试2: 验证LangGraph sender的实例隔离")
    print("-" * 40)
    
    langgraph_instances = []
    for i in range(3):
        sender = AgentFactory.get_sender(test_langgraph_agent_code)
        langgraph_instances.append(sender)
        print(f"第{i+1}次调用 - 实例ID: {id(sender)}, recursion_limit: {sender._recursion_limit}")
    
    # 验证所有实例都不相同
    unique_langgraph_ids = set(id(instance) for instance in langgraph_instances)
    print(f"创建了 {len(langgraph_instances)} 个LangGraph实例，唯一实例数: {len(unique_langgraph_ids)}")
    assert len(unique_langgraph_ids) == len(langgraph_instances), "LangGraph实例隔离失败：存在重复实例"
    print("✓ 测试2通过：LangGraph sender每次调用都创建了新实例")
    print()
    
    # 测试3: 验证不同agent_code返回不同类型的实例
    print("测试3: 验证不同agent_code返回不同类型的实例")
    print("-" * 40)
    
    sender1 = AgentFactory.get_sender(test_agent_code)
    sender2 = AgentFactory.get_sender(test_langgraph_agent_code)
    
    print(f"普通sender类型: {type(sender1).__name__}")
    print(f"LangGraph sender类型: {type(sender2).__name__}")
    
    assert type(sender1) == TestSender, "普通sender类型错误"
    assert type(sender2) == TestLangGraphSender, "LangGraph sender类型错误"
    print("✓ 测试3通过：不同agent_code返回正确的sender类型")
    print()
    
    print("=" * 60)
    print("所有测试通过！AgentFactory用户级别数据隔离功能正常工作")
    print("=" * 60)

if __name__ == "__main__":
    try:
        test_instance_isolation()
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
