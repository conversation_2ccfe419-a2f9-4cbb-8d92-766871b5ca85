import sys
class AppConstant:
    """应用级消息常量"""
    JSON_ERROR = "JSON解析错误: {error}"
    PROCESS_ERROR = "处理异常: {error}"
    OTHER_ERROR = "未知错误"
    DEFAULT_PACKAGE_TYPE = 0
    PACKAGE_TYPE_THINKING = 2
    PROMPT_TYPE_SYSTEM = sys.intern("system")
    PROMPT_TYPE_USER = sys.intern("human")

    class Dynamic:
        OPERATION_COMPLETE = "操作完成，请点击下方预览按钮查看效果~"
        SAVE_FAILED = "配置保存异常,请稍后重试！"
        CHECK_CONFIG = "参数验证"
        SAVE_CONFIG = "保存配置"
        QUERY_PAGE = "查询页面"
        TOOL_CALL_START = "开始调用工具"
        