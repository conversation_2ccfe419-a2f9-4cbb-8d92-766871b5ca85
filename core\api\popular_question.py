import random
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional

from core.vo.user_vo import UserVO
from core.auth.security import check_user
from core.services.database import db_manager
from core.services.database.crud.popular_question import popular_question_crud

router = APIRouter(prefix="/popular-question")


@router.get("")
async def query_popular_questions(
    agent_code: Optional[str] = Query(None, description="智能体编码，不传则查询所有"),
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(4, description="返回的记录数"),
):
    """
    查询热门问题列表

    Args:
        agent_code: 智能体编码，可选
        skip: 跳过的记录数
        limit: 返回的记录数
        user: 当前用户信息

    Returns:
        热门问题列表，按热度降序排序
    """
    try:
        async with db_manager.session() as session:
            if agent_code:
                # 查询指定智能体的热门问题
                questions = await popular_question_crud.get_by_agent_code(
                    session, agent_code=agent_code, skip=skip
                )
            else:
                # 查询所有热门问题
                questions = await popular_question_crud.get_all_ordered_by_sort(
                    session, skip=skip
                )    

            # 从查询结果中随机选择4个问题，如果数量不足则返回全部
            if len(questions) > limit:
                questions = random.sample(questions, limit)

            return {
                "code": 200,
                "data": questions,
                "message": "查询成功",
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.get("/top")
async def query_top_questions(
    agent_code: str = Query(..., description="智能体编码"),
    limit: int = Query(10, description="返回的记录数，默认10条"),
    user: UserVO = Depends(check_user),
):
    """
    查询指定智能体的热门问题TOP N

    Args:
        agent_code: 智能体编码
        limit: 返回的记录数
        user: 当前用户信息

    Returns:
        热门问题TOP N列表
    """
    try:
        async with db_manager.session() as session:
            questions = await popular_question_crud.get_top_questions_by_agent(
                session, agent_code=agent_code, limit=limit
            )

            return {
                "code": 200,
                "data": questions,
                "message": "查询成功",
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.post("/{question_id}/increment-heat")
async def increment_question_heat(
    question_id: int,
    increment: int = Query(1, description="增加的热度值，默认为1"),
    user: UserVO = Depends(check_user),
):
    """
    增加问题的热度值

    Args:
        question_id: 问题ID
        increment: 增加的热度值
        user: 当前用户信息

    Returns:
        更新后的问题信息
    """
    try:
        async with db_manager.session() as session:
            question = await popular_question_crud.increment_heat(
                session, question_id=question_id, increment=increment
            )
            
            if not question:
                raise HTTPException(status_code=404, detail="问题不存在")

            return {
                "code": 200,
                "data": question,
                "message": "热度更新成功",
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"更新热度失败: {str(e)}") from e
