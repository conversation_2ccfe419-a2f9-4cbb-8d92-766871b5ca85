from logging import Logger
from core.config.app_config import config
from fastapi import HTTPException
import requests


def getFilesUrl(ids: list, authorization: str, logger: Logger):
    full_config = config.full_config
    commonPictureUrl = full_config.get("commonPictureUrl")
    logger.info(f"请求图片预览地址配置地址:{commonPictureUrl}")

    if not commonPictureUrl:
        raise HTTPException(status_code=500, detail="图片预览地址未配置")

    response = requests.post(
        commonPictureUrl,
        json=ids,
        headers={"Authorization": authorization},
        timeout=5,
    )

    if response.status_code != 200:
        logger.error("获取图片预览地址失败")

    try:
        json_data = response.json()
    except ValueError as ve:
        logger.error(f"JSON解析失败: {ve}")
    # 请求成功后将对应的url地址赋值到icon字段上
    urls = json_data["data"]

    return urls
