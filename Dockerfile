# Build stage
FROM python:3.11.13-alpine3.22 AS builder

RUN apk update && apk add g++ gcc libc-dev

# Install the uv and uvx binaries
COPY --from=ghcr.io/astral-sh/uv:0.7.8 /uv /uvx /bin/

# Set the working directory
WORKDIR /app

# Copy the pyproject file
COPY pyproject.toml uv.lock .python-version ./

# Install the dependencies
RUN uv sync --frozen --no-cache-dir --verbose

# Runtime stage
FROM python:3.11.13-alpine3.22 AS runtime

# Install the uv and uvx binaries
COPY --from=ghcr.io/astral-sh/uv:0.7.8 /uv /uvx /bin/

# Set the working directory
WORKDIR /app

# Copy installed dependencies from build stage
COPY --from=builder /app/.venv /app/.venv

# Copy project files
COPY pyproject.toml uv.lock .python-version ./

# Copy the rest of the application code
COPY . .

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uv", "run", "main.py"]