from fastapi import APIRouter, HTTPException, Query
from core.config.app_logger import logger, set_log_level
from core.api.response import StandardResponse
import logging
import os

router = APIRouter(prefix="/log", tags=["Log Configuration"])

# 日志配置
# <AUTHOR> xiangjh


@router.post("/level")
async def set_log_level_endpoint(
    level: str = Query(
        ..., 
        description="日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL",
        regex="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$"
    )
):
    """
    动态设置日志级别，无需重启服务即可生效
    
    Args:
        level: 目标日志级别
        
    Returns:
        设置结果
    """
    try:
        # 更新所有logger的级别
        set_log_level(level)
                
        logger.info(f"日志级别已更新为: {level}")
        
        return StandardResponse(
            data={"level": level}
        )
    except Exception as e:
        logger.error(f"设置日志级别失败: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"设置日志级别失败: {str(e)}"
        )


@router.get("/level")
async def get_log_level():
    """
    获取当前日志级别
    
    Returns:
        当前日志级别
    """
    try:
        current_level = os.getenv("LOG_LEVEL", "INFO")
        level_value = getattr(logging, current_level, 20)  # 默认为INFO(20)
        
        return StandardResponse(
            data={"level": current_level, "level_value": level_value}
        )
    except Exception as e:
        logger.error(f"获取日志级别失败: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"获取日志级别失败: {str(e)}"
        )