"""message add hidden field

Revision ID: 558b1c024a94
Revises: 055999258e0e
Create Date: 2025-09-03 17:11:23.177055

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "558b1c024a94"
down_revision: Union[str, None] = "055999258e0e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "ai_agent",
        sa.Column("recursion_limit", sa.INTEGER(), nullable=False, server_default="10"),
    )
    op.add_column(
        "message",
        sa.Column("hidden", sa.BOOLEAN(), nullable=False, server_default="false"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.drop_column("message", "hidden")
    op.alter_column(
        "ai_agent", "recursion_limit", existing_type=sa.INTEGER(), nullable=True
    )
    # ### end Alembic commands ###
