from typing import Any, Optional,Dict
from mcp.server.fastmcp import FastMCP
import requests
import json
from pydantic import BaseModel
from logging import getLogger

logger = getLogger(__name__)

# 定义MCP 服务器，提供工具和资源，将远程的调用都封装在工具函数中
# 注意：MCP 服务器需要和 MCP 客户端在同一台机器上运行，并且采用最高效的stdio传输方式
# @Author: xiangjh

mcp = FastMCP(
    name="McpServer",
    description="一个带有工具、资源和中文提示词的简单 MCP 服务器",
    instructions="""
          这是一个简单的 MCP 服务器，提供以下功能：
        - 资源：
          * stats://server：获取服务器运行时统计信息。
          * langs://available：获取支持的语言列表。
          * info://server：获取服务器基本信息。
        - 工具：
          * say_hello(name: str)：生成个性化的中文问候语并记录连接次数。
          * get_string_length(text: str)：分析输入文本的长度和特性。
          * get_weather(location: str)：Get current weather for a location。
          * order_hotel(city: str)：Book hotels in designated cities。
          * save_page_config(page_id: str, config_json: dict)modifies the configuration information of dynamic pages by providing a page_id and configuring JSON objects.
        - 提示词：
          * greeting_prompt(name: str)：生成中文问候语模板。
        
    """
)

def safe_json_dumps(data: Any, ensure_ascii: bool = False, indent: Optional[int] = None) -> str:
    """
    安全地将对象序列化为 JSON 字符串，失败时返回空对象字符串 '{}'

    :param data: 要序列化的对象
    :param ensure_ascii: 是否确保 ASCII 编码，默认为 False
    :param indent: 缩进空格数，用于美化输出，默认为 None
    :return: JSON 字符串
    """
    try:
        return json.dumps(data, ensure_ascii=ensure_ascii, indent=indent)
    except (TypeError, ValueError) as e:
        print(f"JSON 序列化失败: {e}", exc_info=True)
        return '{}'
    
class GetWeatherInput(BaseModel):
    location: str

class GetHotelInput(BaseModel):
    city: str

# 定义工具函数实现


async def save_page_config(**kwargs: Any) -> str:  # 修改返回类型
    """
    This tool create or update the configuration information of dynamic pages by providing a page_id or dynamicId and configuring JSON objects.
   
    Parameters:
        page_id (str):  Page ID.
        config_json (dict):  Configure JSON objects.
    return:
        json 
    
    """
    param_dict = kwargs.get("kwargs", {})
    page_id = param_dict.get("page_id")
    config_json = param_dict.get("config_json")
    try:
        if not isinstance(config_json, dict):
            raise ValueError("配置必须为JSON对象")
        
        if page_id is not None:  
            config_json["dynamicId"] = page_id
            
        print(f"正在更新页面 {page_id} 的配置") 
        url = "http://172.17.6.116:8002/api/page/system/dynamic/page/manage/insertSqlWithTempForAi"
        
        response = requests.post(
            url,
            json=config_json,
            timeout=10
        )
        # {\"success\":true,\"data\":\"42e1664b-56f0-4b55-b729-6949f30b6b34\",\"page\":null,\"errorCode\":null,\"errorMessage\":null}
        if response.status_code == 200:
            return f"{response.text}"  # 直接返回字符串
        else:
            return f"接口返回错误: {response.status_code}\n{response.text}"
        #return f"页面 {page_id} 配置更新成功"  # 直接返回字符串
    except requests.exceptions.RequestException as e:
        return f"网络连接异常: {str(e)}"
    except json.JSONDecodeError:
        return "接口返回数据解析失败"

def check_page_config(**kwargs: Any) -> str:
    """
    This tool check the configuration information of dynamic pages by providing a page_id or dynamicId and configuring JSON objects.

    Parameters:
        page_id (str): dynamic Page ID.
        config_json (dict):  Configure JSON objects.

    return: json 
    
    """
    param_dict = kwargs.get("kwargs", {})
    page_id = param_dict.get("page_id")
    config_json = param_dict.get("config_json")
    try:
        # 获取配置数据
        if not isinstance(config_json, dict):
            raise ValueError("配置必须为JSON对象")
            
        if page_id is not None:  
            config_json["dynamicId"] = page_id
                
        # 检查 tableParams
        table_params = config_json.get('tableParams', {})
        if not isinstance(table_params, dict):
            raise ValueError("tableParams 必须为字典对象")
            
        # 检查 maintainData
        maintain_data = table_params.get('maintainData')
        if maintain_data and str(maintain_data) == "1":
            if not table_params.get('tableName'):
                raise ValueError("当允许维护数据时，必须指定数据库表名，主键和主键策略")
            
            if not table_params.get('primaryKey'):
                raise ValueError("当允许维护数据时，必须指定数据库表名，主键和主键策略")
            
            if not table_params.get('primaryKeyStrategy'):
                table_params['primaryKeyStrategy'] = "auto"

        # 检查 tableIndexCheckbox
        tableIndexCheckbox = table_params.get('tableIndexCheckbox')
        if tableIndexCheckbox and not tableIndexCheckbox:     
            table_params['tableIndexConfig'] = ""   
                
        # 更新配置中的tableParams
        config_json['tableParams'] = table_params
        
        # 构建成功响应
        response = {
            "success": True,
            "config_json": config_json,
            "resultMessage": "配置验证通过，请继续调用 save_page_config 工具进行保存"
        }
        
    except Exception as e:
        logger.info(f"构建错误响应: {str(e)}")
        # 构建错误响应
        response = {
            "success": False,
            "config_json": config_json,
            "resultMessage": str(e)
        }
    
    # 返回JSON字符串
    return safe_json_dumps(response)
    


async def call_nl2sql_function(**kwargs: Any) -> str:  
    """
     该方法是一个NL2Sql的工具，它可以根据用户输入的自然语言描述，自动转换成标准的查询sql语句。

    Parameters:
        query (str):  用户输入的自然语言描述。

    Returns:
        json:  {\"success\":true,\"sql\":\"select * from table_name;\"}
    """

   
    param_dict = kwargs.get("kwargs", {})
    query = param_dict.get("query")
    try:
            
        logger.info(f"call_nl2sql: {query}") 
        url = "http://172.17.9.46:8003/nl2sql"
        payload = {
            "query": query,
            "connection_id": 35
        }
        response = requests.post(
            url,
            json=payload,
            timeout=50
        )
        # { "success": true, "sql": "select * from table_name;" }
        if response.status_code == 200:
           result = json.loads(response.text)
           return _process_result(result)
        else:
            return f"接口返回错误: {response.status_code}\n{response.text}"
    except requests.exceptions.RequestException as e:
        return f"网络连接异常: {str(e)}"
    except json.JSONDecodeError:
        logger.error(f"接口返回数据解析失败！")
        return "接口返回数据解析失败"
    except Exception as e:
        logger.error(f"处理过程中发生意外错误: {str(e)}")
        return f"系统错误: {str(e)}"

def _process_result(result: Dict[str, Any]) -> Optional[str]:
    """自定义结果处理器"""
    if result.get("success"):
        raw_sql = result['sql']
        # 美化SQL格式
        formatted_sql = _format_sql(raw_sql)
        response = {
            "success": True,
            "sql": formatted_sql,
            "resultMessage": "生成sql成功"
        }
    else:
        error_msg = result.get('error', '未知错误')
        response = {
            "success": False,
            "sql": None,
            "resultMessage": error_msg
        }

    return safe_json_dumps(response)

@mcp.tool()
async def get_myweather(city: str) -> str:
    """Get current weather for a city"""
    # 这里可以替换为真实的API调用
    print(f"查询{city}天气")
    return f"{city}当前天气：25℃，晴"


async def query_companyincome(companycode: str = None) -> str:
    """
    查询指定companycode的公司名称的营业收入，返回一个包含公司名称和收入的列表
    
    Parameters:
        company_name (str, optional): 公司名称，如果不提供则返回一些示例数据
        
    Returns:
        str: JSON格式的公司收入数据列表
    """
    sample_data = [
        {"companycode": "000001","companyname": "苹果公司", "income": 394320000000},
        {"companycode": "000002","companyname": "微软公司", "income": 211910000000},
        {"companycode": "000003","companyname": "谷歌公司", "income": 282830000000},
        {"companycode": "000004","companyname": "亚马逊", "income": 574780000000},
        {"companycode": "000005","companyname": "特斯拉", "income": 96773000000},
        {"companycode": "000006","companyname": "英伟达", "income": 60922000000},
        {"companycode": "000007","companyname": "阿里巴巴", "income": 132070000000},
        {"companycode": "000008","companyname": "腾讯", "income": 869120000000},
        {"companycode": "000009","companyname": "深圳万科", "income": 68889},
        {"companycode": "000010","companyname": "成都万科", "income": 45000},
    ]
    
    # 如果提供了公司名称，则筛选匹配的公司
    if companycode:
        filtered_data = [item for item in sample_data if companycode in item["companycode"]]
        if not filtered_data:
            # 如果没有匹配的公司，返回一条默认数据
            filtered_data = [{"companyname": companycode, "income": 0}]
        result = filtered_data
    else:
        # 如果没有提供公司名称，返回空数据
        result = []
    
    return safe_json_dumps({"success": True, "data": result})
@mcp.tool()
async def query_company_info(company_name: str = None) -> str:
    """
    查询指定公司名称查询公司信息，如果查询到多笔，则返回列表，否则返回空列表。
    
    Parameters:
        company_name (str, optional): 公司名称，如果不提供则返回一些示例数据
        
    Returns:
        str: JSON格式的公司收入数据列表
    """
    all_data = [
        {"companycode": "000001", "companyname": "苹果公司"},
        {"companycode": "000002", "companyname": "微软公司"},
        {"companycode": "000003", "companyname": "谷歌公司"},
        {"companycode": "000004", "companyname": "亚马逊"},    
        {"companycode": "000005", "companyname": "特斯拉"},
        {"companycode": "000006", "companyname": "英伟达"},
        {"companycode": "000007", "companyname": "阿里巴巴"},
        {"companycode": "000008", "companyname": "腾讯"},
        {"companycode": "000009", "companyname": "深圳万科"},
        {"companycode": "000010", "companyname": "成都万科"},
    ]

    # 如果提供了公司名称，则筛选匹配的公司
    if company_name:
        filtered_data = [item for item in all_data if company_name in item["companyname"]]
        if not filtered_data:
            # 如果没有匹配的公司，返回一条默认数据
            filtered_data = [{"companyname": company_name, "companytype": "未知", "companyaddress": "未知"}]
        result = filtered_data
    else:
        # 如果没有提供公司名称，返回空数据
        result = []
    
    return safe_json_dumps({"success": True, "data": result})



def _format_sql(sql: str) -> str:
    """SQL格式化工具"""
    return (sql.replace('\\\\n', '\n')
               .replace('\\\\t', '\t')
               .replace('\\n', '\n')
               .replace('\\t', '    ')
               .replace("\\'", "'"))

if __name__ == "__main__":
    mcp.run(transport='stdio')