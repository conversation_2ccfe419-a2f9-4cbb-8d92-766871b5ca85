from typing import List, Optional
from fastapi import HTTPException
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select,delete
from datetime import datetime, timezone

from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.prompt_template import (
    PromptTemplateTable,
    PromptTemplateCreate,
    PromptTemplateUpdate,
    UserPromptTemplateCreate,
    UserPromptTemplateUpdate,
)


class CRUDPromptTemplate(
    CRUDBase[PromptTemplateTable, PromptTemplateCreate, PromptTemplateUpdate]
):
    """提示词模板CRUD操作实现"""

    async def get_by_type(
        self, db: AsyncSession, *, template_type: str, skip: int = 0, limit: int = 100
    ) -> List[PromptTemplateTable]:
        """根据模板类型获取提示词模板列表，按sort字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .where(PromptTemplateTable.type == template_type)
            .order_by(PromptTemplateTable.sort.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_group(
        self, db: AsyncSession, *, group: str, skip: int = 0, limit: int = 100
    ) -> List[PromptTemplateTable]:
        """根据分组获取提示词模板列表，按sort字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .where(PromptTemplateTable.group == group)
            .order_by(PromptTemplateTable.sort.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_type_and_group(
        self,
        db: AsyncSession,
        *,
        template_type: str,
        group: str,
        skip: int = 0,
        limit: int = 100,
    ) -> List[PromptTemplateTable]:
        """根据模板类型和分组获取提示词模板列表，按sort字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .where(
                PromptTemplateTable.type == template_type,
                PromptTemplateTable.group == group,
            )
            .order_by(PromptTemplateTable.sort.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_sorted(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[PromptTemplateTable]:
        """获取所有提示词模板，按sort字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .order_by(PromptTemplateTable.sort.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_system_sorted(
        self, db: AsyncSession, *, agent_code: str
    ) -> List[PromptTemplateTable]:
        """获取所有提示词模板，按sort字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .order_by(PromptTemplateTable.sort.asc(), PromptTemplateTable.id.asc())
            .where(
                PromptTemplateTable.agent_code == agent_code,
                PromptTemplateTable.type == "system",
                PromptTemplateTable.disabled == "0",
            )
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_groups(self, db: AsyncSession) -> List[str]:
        """获取所有不同的分组列表"""
        query = select(PromptTemplateTable.group).distinct()
        result = await db.execute(query)
        return result.scalars().all()

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: PromptTemplateTable,
        obj_in: PromptTemplateUpdate,
    ) -> PromptTemplateTable:
        """更新提示词模板，自动设置update_time时间"""
        obj_data = obj_in.model_dump(exclude_unset=True)
        obj_data["update_time"] = datetime.now(timezone.utc)

        for field, value in obj_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def search_by_title_or_description(
        self, db: AsyncSession, *, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[PromptTemplateTable]:
        """根据标题或描述搜索提示词模板"""
        search_pattern = f"%{search_term}%"
        query = (
            select(PromptTemplateTable)
            .where(
                (PromptTemplateTable.title.ilike(search_pattern))
                | (PromptTemplateTable.description.ilike(search_pattern))
            )
            .order_by(PromptTemplateTable.sort.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def change_template_sort(self, db: AsyncSession, *, id: int, prev_id: int):
        prev_template = await prompt_template_crud.get(db, _id=prev_id)
        existing_template = await prompt_template_crud.get(db, _id=id)
        current_sort = existing_template.sort
        prev_sort = prev_template.sort if prev_template is not None else 0

        # TODO: 锁行
        if current_sort > prev_sort:
            sort_update = text("""
                    UPDATE prompt_template
                    SET sort = sort + 1 
                    WHERE sort > :new_sort AND sort <= :old_sort
                """)
            new_sort = prev_sort + 1
        else:
            sort_update = text("""
                    UPDATE prompt_template
                    SET sort = sort - 1 
                    WHERE sort > :old_sort AND sort <= :new_sort
                """)
            new_sort = prev_sort

        # 临时设置 sort = -1
        await db.execute(
            text("""
                UPDATE prompt_template SET sort = -1 WHERE id = :id;
                """),
            {
                "id": id,
                "old_sort": current_sort,
            },
        )

        await db.execute(
            sort_update,
            {"old_sort": current_sort, "new_sort": prev_sort},
        )

        # 设置新的 sort
        await db.execute(
            text("""
                UPDATE prompt_template SET sort = :new_sort + 1 WHERE id = :id;
                """),
            {
                "id": id,
                "new_sort": new_sort,
            },
        )

        return True

    # User-specific CRUD methods
    async def get_user_templates(
        self, db: AsyncSession, *, user_id: str, agent_code: str
    ) -> List[PromptTemplateTable]:
        """获取指定用户的所有提示词模板，按sort字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .where(
                PromptTemplateTable.type == "user",
                PromptTemplateTable.create_by == user_id,
                PromptTemplateTable.agent_code == agent_code,
            )
            .order_by(PromptTemplateTable.sort.desc(), PromptTemplateTable.id.desc())
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_user_template_by_id(
        self, db: AsyncSession, *, template_id: int, user_id: str
    ) -> Optional[PromptTemplateTable]:
        """获取指定用户的单个提示词模板"""
        query = select(PromptTemplateTable).where(
            PromptTemplateTable.id == template_id,
            PromptTemplateTable.type == "user",
            PromptTemplateTable.create_by == user_id,
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def create_user_template(
        self, db: AsyncSession, *, obj_input: UserPromptTemplateCreate, user_id: str
    ) -> PromptTemplateTable:
        """创建用户提示词模板"""

        # 将UserPromptTemplateCreate转换为PromptTemplateCreate
        template_data = obj_input.model_dump()
        template_data["create_by"] = user_id
        template_data["update_by"] = user_id
        template_data["type"] = "user"  # 确保类型为user

        template_create = UserPromptTemplateCreate(**template_data)
        return await self.create(db, obj_input=template_create)

    async def update_user_template(
        self,
        db: AsyncSession,
        *,
        db_obj: PromptTemplateTable,
        obj_in: UserPromptTemplateUpdate,
        user_id: str,
    ) -> PromptTemplateTable:
        """更新用户提示词模板，包含所有权验证"""
        # 验证模板所有权
        if db_obj.create_by != user_id or db_obj.type != "user":
            raise ValueError("用户无权限修改此模板")

        # 更新updated_by字段
        obj_data = obj_in.model_dump(exclude_unset=True)
        obj_data["update_by"] = user_id
        obj_data["update_time"] = datetime.now(timezone.utc)

        for field, value in obj_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def delete_user_template(
        self, db: AsyncSession, *, template_id: int, user_id: str
    ) -> bool:
        """删除用户提示词模板，包含所有权验证"""
        template = await self.get_user_template_by_id(
            db, template_id=template_id, user_id=user_id
        )
        if not template:
            return False

        await self.remove(db, _id=template_id)
        return True
    
    async def delete_template_by_agent_code(
        self, db: AsyncSession, agent_code: str
    ) -> bool:
        """根据agente_code删除用户提示词模板"""
        stmt = delete(PromptTemplateTable).where(PromptTemplateTable.agent_code == agent_code)
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount > 0

    async def search_user_templates(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
    ) -> List[PromptTemplateTable]:
        """搜索指定用户的提示词模板"""
        search_pattern = f"%{search_term}%"
        query = (
            select(PromptTemplateTable)
            .where(
                PromptTemplateTable.type == "user",
                PromptTemplateTable.create_by == user_id,
                (
                    (PromptTemplateTable.title.ilike(search_pattern))
                    | (PromptTemplateTable.description.ilike(search_pattern))
                ),
            )
            .order_by(PromptTemplateTable.sort.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    def validate_user_ownership(
        self, template: PromptTemplateTable, user_id: str
    ) -> bool:
        """验证用户是否拥有指定模板的权限"""
        return (
            template is not None
            and template.type == "user"
            and template.create_by == user_id
        )

    async def change_user_template_sort(
        self, db: AsyncSession, *, id: int, prev_id: int, user_id: str
    ):
        prev_template = await prompt_template_crud.get(db, _id=prev_id)
        existing_template = await prompt_template_crud.get(db, _id=id)
        if existing_template.create_by != user_id:
            raise HTTPException(status_code=403, detail="没有修改权限")
        current_sort = existing_template.sort
        prev_sort = prev_template.sort if prev_template is not None else 0

        if prev_sort == current_sort:
            return True

        if prev_template and prev_template.create_by != user_id:
            raise HTTPException(
                status_code=403, detail="错误的修改项，prev_template 不是当前用户创建"
            )

        # TODO: 锁行
        if current_sort > prev_sort:
            sort_update = text("""
                UPDATE prompt_template
                SET sort = sort + 1 
                WHERE sort >= :new_sort AND sort < :old_sort
                AND create_by = :user_id
            """)
        else:
            sort_update = text("""
                UPDATE prompt_template
                SET sort = sort - 1 
                WHERE sort > :old_sort AND sort <= :new_sort
                AND create_by = :user_id
            """)
        new_sort = prev_sort

        # 临时设置 sort = -1
        await db.execute(
            text("""
            UPDATE prompt_template SET sort = -1 WHERE id = :id;
            """),
            {
                "id": id,
            },
        )

        await db.execute(
            sort_update,
            {"old_sort": current_sort, "new_sort": prev_sort, "user_id": user_id},
        )

        # 设置新的 sort
        await db.execute(
            text("""
            UPDATE prompt_template SET sort = :new_sort WHERE id = :id;
            """),
            {
                "id": id,
                "new_sort": new_sort,
            },
        )

        return True


# 创建CRUD实例
prompt_template_crud = CRUDPromptTemplate(PromptTemplateTable)
