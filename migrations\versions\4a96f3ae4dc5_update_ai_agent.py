"""update ai_agent

Revision ID: 4a96f3ae4dc5
Revises: c5097578b772
Create Date: 2025-06-05 10:15:22.534399

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '4a96f3ae4dc5'
down_revision: Union[str, None] = 'c5097578b772'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ai_agent', sa.Column('router_url', sa.String(length=255), nullable=True))
    op.add_column('ai_agent', sa.Column('status', sa.Integer(), nullable=True, server_default='1'))
    op.add_column('ai_agent', sa.Column('recursion_limit', sa.Integer(), nullable=True, server_default='15'))
    op.alter_column('ai_agent', 'agent_name',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('ai_agent', 'target_type',
               existing_type=sa.VARCHAR(length=30),
               nullable=True)
    op.alter_column('ai_agent', 'base_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'api_key',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'prompt_text',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ai_agent', 'prompt_text',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('ai_agent', 'api_key',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'base_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'target_type',
               existing_type=sa.VARCHAR(length=30),
               nullable=True)
    op.alter_column('ai_agent', 'agent_name',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.drop_column('ai_agent', 'status')
    op.drop_column('ai_agent', 'router_url')
    # ### end Alembic commands ###
