"""智能体管理新增sort字段

Revision ID: 4806ea67937c
Revises: 25891fe6c978
Create Date: 2025-07-17 17:15:26.071802

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '4806ea67937c'
down_revision: Union[str, None] = '25891fe6c978'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ai_agent', sa.Column('sort', sqlmodel.sql.sqltypes.AutoString(), nullable=False,server_default='0'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ai_agent', 'sort')
    # ### end Alembic commands ###
