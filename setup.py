#!/usr/bin/env python3
"""
Setup script for agent-server package
"""

from setuptools import setup, find_packages
import os

# Read the contents of README file
this_directory = os.path.abspath(os.path.dirname(__file__))
with open(os.path.join(this_directory, 'README.md'), encoding='utf-8') as f:
    long_description = f.read()

# Read requirements
def read_requirements(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="agent-server",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A comprehensive AI agent framework with LangChain, LangGraph, and MCP support",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/agent-server",
    packages=find_packages(exclude=["tests*"]),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.11",
    install_requires=[
        "alembic>=1.15.2",
        "asyncpg>=0.30.0",
        "dotenv>=0.9.9",
        "fastapi[standard]>=0.115.11",
        "httpx>=0.28.1",
        "langchain-community>=0.3.24",
        "langchain[openai]>=0.3.25",
        "langgraph>=0.1.0",
        "loguru>=0.7.3",
        "mcp>=1.9.0",
        "pillow>=11.1.0",
        "psycopg2-binary>=2.9.10",
        "psycopg[binary,pool]>=3.2.6",
        "pylint>=3.3.6",
        "redis>=6.1.0",
        "sqlalchemy>=2.0.41",
        "sqlmodel>=0.0.24",
        "pyyaml>=6.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "flake8>=6.0.0",
            "mypy>=1.7.0",
        ],
        "test": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "agent-server=cli:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords="ai agent langchain langgraph mcp llm",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/agent-server/issues",
        "Source": "https://github.com/yourusername/agent-server",
        "Documentation": "https://agent-server.readthedocs.io/",
    },
)
