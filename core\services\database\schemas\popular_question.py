from datetime import datetime, timezone
from typing import Optional
from sqlmodel import Field, SQLModel, Column, DateTime


class PopularQuestionBase(SQLModel):
    """热门问题基础模型"""
    agent_code: str = Field(index=True, description="智能体编码")
    prompt: str = Field(description="问题提示词")
    heat: Optional[int] = Field(default=0, description="热度值")
    disabled: str = Field(default='0', description="是否启用")
    sort: int = Field(default=0, description="排序顺序，用于列表显示（升序）")
    create_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="创建时间"
    )
    update_time: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="更新时间"
    )
    create_by: str = Field(description="创建者")
    update_by: Optional[str] = Field(description="修改者")

class PopularQuestionTable(PopularQuestionBase, table=True):
    """数据库中的热门问题模型"""
    __tablename__ = "popular_question"
    id: int = Field(default=None, primary_key=True)


class PopularQuestionCreate(PopularQuestionBase):
    """创建热门问题的模型"""


class PopularQuestionUpdate(SQLModel):
    """更新热门问题的模型"""
    agent_code: Optional[str] = None
    prompt: Optional[str] = None
    heat: Optional[int] = None


class PopularQuestionRead(PopularQuestionBase):
    """API响应中的热门问题模型"""
    id: int
