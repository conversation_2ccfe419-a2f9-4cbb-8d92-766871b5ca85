"""调整模型管理中code唯一

Revision ID: 055999258e0e
Revises: 379a8233d7e6
Create Date: 2025-08-18 15:53:12.301294

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '055999258e0e'
down_revision: Union[str, None] = '379a8233d7e6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('agent_prompt', 'p_type',
               existing_type=sa.VARCHAR(length=30),
               nullable=False)
    op.alter_column('agent_prompt', 'status',
               existing_type=sa.INTEGER(),
               nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('agent_prompt', 'create_time',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('agent_prompt', 'update_time',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('agent_prompt', 'create_by',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               nullable=False)
    op.alter_column('agent_prompt', 'update_by',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.drop_column('ai_agent', 'recursion_limit')
    op.create_unique_constraint(None, 'model_management', ['model_code'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'model_management', type_='unique')
    op.add_column('ai_agent', sa.Column('recursion_limit', sa.INTEGER(), autoincrement=False, nullable=True))
    op.alter_column('agent_prompt', 'update_by',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('agent_prompt', 'create_by',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.INTEGER(),
               nullable=True)
    op.alter_column('agent_prompt', 'update_time',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True)
    op.alter_column('agent_prompt', 'create_time',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True)
    op.alter_column('agent_prompt', 'status',
               existing_type=sa.INTEGER(),
               nullable=True,
               existing_server_default=sa.text('0'))
    op.alter_column('agent_prompt', 'p_type',
               existing_type=sa.VARCHAR(length=30),
               nullable=True)
    # ### end Alembic commands ###
