"""修改heat字段可以为空

Revision ID: 1477f55f3216
Revises: 6d42f6a9a9fe
Create Date: 2025-07-22 18:34:07.290357

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '1477f55f3216'
down_revision: Union[str, None] = '6d42f6a9a9fe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('popular_question', 'heat',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('popular_question', 'heat',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
