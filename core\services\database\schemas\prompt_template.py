from datetime import datetime, timezone
from typing import Optional
from sqlmodel import Field, SQLModel, Column, DateTime
from pydantic import field_validator


class PromptTemplateBase(SQLModel):
    """提示词模板基础模型"""

    type: str = Field(description="模板类型：user 或 system")
    title: str = Field(description="模板标题/显示名称")
    description: Optional[str] = Field(default="", description="模板详细描述，说明用途和使用方法")
    prompt: str = Field(description="实际的提示词内容/模板文本")
    agent_code: str = Field(default="", description="智能体代码")
    group: Optional[str] = Field(default=None, description="分组标识符，用于组织模板")
    preview: Optional[str] = Field(default=None, description="模板预览文本或摘要")
    sort: int = Field(default=0, description="排序顺序，用于列表显示（升序）")
    disabled: str = Field(default="0", description="是否启用")
    create_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="创建时间",
    )
    update_time: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="更新时间",
    )
    create_by: str = Field(description="创建者")
    update_by: Optional[str] = Field(description="修改者")

    @field_validator("type")
    @classmethod
    def validate_type(cls, v):
        """验证type字段只能是user或system"""
        if v not in ["user", "system"]:
            raise ValueError('type must be either "user" or "system"')
        return v

    @field_validator("title", "prompt")
    @classmethod
    def validate_required_strings(cls, v):
        """验证必填字符串字段不能为空"""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class PromptTemplateTable(PromptTemplateBase, table=True):
    """数据库中的提示词模板模型"""

    __tablename__ = "prompt_template"

    id: int = Field(default=None, primary_key=True)


class PromptTemplateCreate(PromptTemplateBase):
    """创建提示词模板的模型"""

    pass


class PromptTemplateUpdate(SQLModel):
    """更新提示词模板的模型"""

    type: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    prompt: Optional[str] = None
    agent_code: Optional[str] = None
    group: Optional[str] = None
    preview: Optional[str] = None
    sort: Optional[int] = None
    update_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc), description="更新时间"
    )

    @field_validator("type")
    @classmethod
    def validate_type(cls, v):
        """验证type字段只能是user或system"""
        if v is not None and v not in ["user", "system"]:
            raise ValueError('type must be either "user" or "system"')
        return v

    @field_validator("title", "description", "prompt", "group", "preview")
    @classmethod
    def validate_optional_strings(cls, v):
        """验证可选字符串字段不能为空字符串"""
        if v is not None and (not v or not v.strip()):
            raise ValueError("Field cannot be empty if provided")
        return v.strip() if v else v


class PromptTemplateRead(PromptTemplateBase):
    """API响应中的提示词模板模型"""

    id: int


class UserPromptTemplateCreate(SQLModel):
    """创建用户提示词模板的模型 - 强制type为user"""

    title: str = Field(description="模板标题/显示名称")
    description: Optional[str] = Field(default="", description="模板详细描述，说明用途和使用方法")
    prompt: str = Field(description="实际的提示词内容/模板文本")
    agent_code: str = Field(default="", description="智能体代码")
    group: Optional[str] = Field(default=None, description="分组标识符，用于组织模板")
    preview: Optional[str] = Field(default=None, description="模板预览文本或摘要")
    sort: int = Field(default=0, description="排序顺序，用于列表显示（升序）")
    disabled: Optional[str] = Field(default="0", description="是否启用")
    # type字段固定为"user"，不允许用户修改
    type: str = Field(default="user", description="模板类型：固定为user")
    create_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="创建时间",
    )
    update_time: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="更新时间",
    )
    create_by: Optional[str] = Field(default="", description="创建者")
    update_by: Optional[str] = Field(default="", description="修改者")

    @field_validator("type")
    @classmethod
    def validate_type_is_user(cls, v):
        """验证type字段必须是user"""
        if v != "user":
            raise ValueError('type must be "user" for user templates')
        return v

    @field_validator("title", "prompt")
    @classmethod
    def validate_required_strings(cls, v):
        """验证必填字符串字段不能为空"""
        if not v or not v.strip():
            raise ValueError("Field cannot be empty")
        return v.strip()


class UserPromptTemplateUpdate(SQLModel):
    """更新用户提示词模板的模型 - 不允许修改type字段"""

    title: Optional[str] = None
    description: Optional[str] = None
    prompt: Optional[str] = None
    agent_code: Optional[str] = None
    group: Optional[str] = None
    preview: Optional[str] = None
    sort: Optional[int] = None
    disabled: Optional[bool] = None
    update_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc), description="更新时间"
    )

    # 不允许修改type字段，确保始终为user

    @field_validator("title", "prompt", "group", "preview")
    @classmethod
    def validate_optional_strings(cls, v):
        """验证可选字符串字段不能为空字符串"""
        if v is not None and (not v or not v.strip()):
            raise ValueError("Field cannot be empty if provided")
        return v.strip() if v else v
