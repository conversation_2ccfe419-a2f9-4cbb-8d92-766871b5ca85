from core.config.app_config import config
from core.config.app_logger import logger
from core.auth.default_auth_adapter import DefaultAuthAdapter
from core.auth.adapter import AuthAdapter
from importlib import import_module

# 适配器工厂类
# <AUTHOR> xiangjh
class AuthAdapterFactory:
    _current_adapter = None

    @classmethod
    def initialize(cls):
        """初始化方法，注册系统默认适配器"""
        try:
           module_path, class_name = config.auth.adapter.rsplit('.', 1)
           module = import_module(module_path)
           adapter_class = getattr(module, class_name)
           cls._current_adapter = adapter_class()
           logger.info(f"成功加载认证适配器: {config.auth.adapter}")
        except Exception as e:
            logger.error(f"适配器加载失败，使用默认适配器: {str(e)}")
            cls._current_adapter = DefaultAuthAdapter()


    @classmethod
    def get_current(cls) -> AuthAdapter:
        if not cls._current_adapter:
            cls.initialize()
        return cls._current_adapter


def get_auth_adapter() -> AuthAdapter:
    return AuthAdapterFactory.get_current()