from datetime import datetime, timezone
from typing import List

from fastapi import APIRouter, Depends, Body, HTTPException
import re

from core.vo.user_vo import UserVO
from core.api.response import StandardResponse, Pagination
from core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationUpdate,
    ConversationBatchDelete
)
from core.auth.security import check_user
from core.services.database import db_manager
from core.services.database.crud.conversation import conversation_curd

router = APIRouter(prefix="/conversation", tags=["Conversation"])


@router.get("")
async def query_conversation_by_user_id(
    user: UserVO = Depends(check_user),
    size: int = 10,
    page: int = 1,
    keyword: str = "",
):
    """
    根据用户ID查询会话列表

    Args:
        user: 当前用户信息
        size: 每页记录数
        page: 页码，从1开始

    Returns:
        会话列表
    """
    try:
        async with db_manager.session() as session:
            # 使用conversation_curd中的查询方法
            result = (
                await conversation_curd.query_conversations_by_user_with_pagination(
                    db=session,
                    user_id=user.userId,
                    keyword=keyword,
                    size=size,
                    offset=(page - 1) * size,
                )
            )

            conversation_rows = result["conversations"]
            total_count = result["total_count"]

            pagination = Pagination(page=page, pageSize=size, total=total_count)

            return StandardResponse(
                data=conversation_rows, message="查询成功", pagination=pagination
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.post("/")
async def create_conversation(
    user: UserVO = Depends(check_user),
    body = Body(..., description=""),
):
    """
    新建会话

    Args:
        user: 当前用户信息

    Returns:
        会话ID
    """
    
    conversation_name = body.conversation_name
    try:
        async with db_manager.session() as session:
            # 创建新的会话记录
            new_conversation = ConversationCreate(
                user_id=user.userId,
                name=conversation_name,
                created_at=datetime.now(timezone.utc),
            )
            conversation_curd.create(db=session, obj_input=new_conversation)

            return StandardResponse(
                data={"conversation_id": new_conversation["id"]},
                code="201",
                message="会话创建成功",
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}") from e


@router.put("/{conversation_id}")
async def update_conversation(
    conversation_id,
    data=Body(..., description="会话"),
):
    """
    更新会话

    Args:
        conversation_id: 会话ID
        data: 会话

    Returns:
        会话ID
    """
    try:
        async with db_manager.session() as session:
            # 更新会话记录
            conversation_list = await conversation_curd.get_by_conversation_id(
                session, _id=conversation_id
            )
            conversation = conversation_list[0]
            if not conversation:
                raise HTTPException(status_code=404, detail="会话不存在")

            # 更新会话，仅能修改名称
            new_conversation = ConversationUpdate(title=data["title"])
            await conversation_curd.update(
                db=session, db_obj=conversation, obj_input=new_conversation
            )

            return StandardResponse(data=None, message="会话更新成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新会话失败: {str(e)}") from e


@router.delete("/{conversation_id}")
async def delete_conversation(
    conversation_id,
):
    """
    软删除会话

    Args:
        conversation_id: 会话ID

    Returns:
        会话ID
    """
    try:
        async with db_manager.session() as session:
            # 软删除会话记录
            deleted_conversation = await conversation_curd.soft_delete(
                db=session, _id=conversation_id
            )

            if not deleted_conversation:
                raise HTTPException(status_code=404, detail="会话不存在")

            return StandardResponse(data=None, message="会话删除成功")
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"删除会话失败: {str(e)}") from e


@router.post("/batch-delete")
async def batch_delete_conversations(
    body: ConversationBatchDelete = Body(..., description="批量删除参数"),
    user: UserVO = Depends(check_user),
):
    """
    批量软删除会话

    Args:
        ids: 批量删除请求数据，包含会话ID列表
        user: 当前用户信息

    Returns:
        批量删除结果
    """
    ids = body.ids
    try:
        if not ids:
            raise HTTPException(status_code=400, detail="会话ID列表不能为空")

        # if len(ids) > 100:
        #     raise HTTPException(status_code=400, detail="单次批量删除不能超过100个会话")

        async with db_manager.session() as session:
            # 批量软删除会话记录
            result = await conversation_curd.batch_soft_delete(
                db=session,
                conversation_ids=ids,
                user_id=user.userId
            )

            if result["deleted_count"] == 0:
                return StandardResponse(
                    data=result,
                    message="没有找到可删除的会话",
                    code="404"
                )

            message = f"成功删除 {result['deleted_count']} 个会话"
            if result["not_found_ids"]:
                message += f"，{len(result['not_found_ids'])} 个会话未找到或已删除"

            return StandardResponse(data=result, message=message)
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"批量删除会话失败: {str(e)}") from e


@router.get("/search")
async def search_conversation_by_message(
    user: UserVO = Depends(check_user),
    size: int = 10,
    page: int = 1,
    agent_code: str = "",
    keyword: str = "",
):
    """
    根据关键字查询消息，并返回会话列表
    """

    try:
        async with db_manager.session() as session:
            # 使用conversation_curd中的搜索方法
            conversation_rows = await conversation_curd.search_conversations_by_message(
                db=session,
                user_id=user.userId,
                keyword=keyword,
                agent_code=agent_code,
                size=size,
                offset=(page - 1) * size,
            )

            results = []
            # 合并消息data
            for item in conversation_rows:
                content = ""
                if isinstance(item.content, list):
                    for data in item.content:
                        if data.get("package_type") == 0:
                            content += data.get("data")
                        # replace <message-embedded> to ""
                        content = re.sub(
                            r"<message-embedded>[\s\S]*</message-embedded>", "", content
                        )
                new_item = {
                    "id": item.id,
                    "title": item.title,
                    "content": content,
                    "current_agent_code": item.current_agent_code,
                    "updated_at": item.updated_at,
                }
                results.append(new_item)

            pagination = Pagination(
                page=page,
                pageSize=size,
            )

            return StandardResponse(
                data=results, message="查询成功", pagination=pagination
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


# 通过id查询会话详情
@router.get("/{conversation_id}")
async def query_conversation_by_id(
    conversation_id,
):
    """
    通过id查询会话详情

    Args:
        conversation_id: 会话ID

    Returns:
        会话详情
    """
    try:
        async with db_manager.session() as session:
            # 查询会话详情
            conversation_list = await conversation_curd.get_by_conversation_id(
                session, _id=conversation_id
            )
            if len(conversation_list) == 0:
                raise HTTPException(status_code=404, detail="会话不存在")
            conversation = conversation_list[0]

            return StandardResponse(data=conversation, message="查询成功")
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"查询会话失败: {str(e)}") from e


# 管理员功能：查询已删除的会话
@router.get("/admin/deleted")
async def query_deleted_conversations(
    user: UserVO = Depends(check_user),
    size: int = 10,
    page: int = 1,
):
    """
    查询已删除的会话列表（管理员功能）

    Args:
        user: 当前用户信息
        size: 每页记录数
        page: 页码，从1开始

    Returns:
        已删除的会话列表
    """
    try:
        async with db_manager.session() as session:
            # 获取已删除的会话列表
            deleted_conversations = await conversation_curd.get_deleted_conversations(
                db=session, user_id=user.userId, skip=(page - 1) * size, limit=size
            )

            return StandardResponse(data=deleted_conversations, message="查询成功")
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"查询已删除会话失败: {str(e)}"
        ) from e


# 管理员功能：恢复已删除的会话
@router.post("/admin/restore/{conversation_id}")
async def restore_conversation(
    conversation_id: str,
    user: UserVO = Depends(check_user),
):
    """
    恢复已删除的会话（管理员功能）

    Args:
        conversation_id: 会话ID
        user: 当前用户信息

    Returns:
        恢复结果
    """
    try:
        async with db_manager.session() as session:
            # 恢复会话记录
            restored_conversation = await conversation_curd.restore(
                db=session, _id=conversation_id
            )

            if not restored_conversation:
                raise HTTPException(status_code=404, detail="会话不存在")

            return StandardResponse(data=None, message="会话恢复成功")
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"恢复会话失败: {str(e)}") from e


# 管理员功能：永久删除会话
@router.delete("/admin/hard-delete/{conversation_id}")
async def hard_delete_conversation(
    conversation_id: str,
    user: UserVO = Depends(check_user),
):
    """
    永久删除会话（管理员功能）

    Args:
        conversation_id: 会话ID
        user: 当前用户信息

    Returns:
        删除结果
    """
    try:
        async with db_manager.session() as session:
            # 永久删除会话记录
            deleted_conversation = await conversation_curd.remove(
                db=session, _id=conversation_id, hard_delete=True
            )

            if not deleted_conversation:
                raise HTTPException(status_code=404, detail="会话不存在")

            return StandardResponse(data=None, message="会话永久删除成功")
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=500, detail=f"永久删除会话失败: {str(e)}"
        ) from e
