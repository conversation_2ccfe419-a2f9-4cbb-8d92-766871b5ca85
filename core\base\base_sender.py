from abc import ABC, abstractmethod
from typing import AsyncIterator, Any,AsyncGenerator, Optional
from fastapi import Request
from core.message.transmitter import Transmitter
from core.config.app_logger import logger
from utils.common_utils import CommonUtils
import asyncio
import traceback
# 发送器抽象基类（BaseSender），用于封装流式响应生成逻辑。 
# <AUTHOR> xiangjh
class BaseSender(ABC):
    """
    该类提供统一的接口来处理与大模型交互后的消息发送流程，
    支持异步流式响应、会话管理、错误处理和日志记录等通用功能。
    子类需继承并实现 [_generate_content()]方法以完成具体的消息生成逻辑。
    
    Attributes:
        agent_code (str): 当前 Agent 的唯一标识符，用于会话 ID 生成及日志记录。
    """
    def __init__(self, agent_code: str):
        """
        初始化 BaseSender 实例。
        
        Args:
            agent_code (str): 当前 Agent 的唯一标识符，用于：
                - 生成会话 ID（session_id）
                - 日志记录上下文识别
                - 区分不同 Agent 的行为逻辑（可选）
        """
        self.agent_code = agent_code
        self.transmitter: Optional[Transmitter] = None 

    async def generate_stream(
        self,
        transmitter: Transmitter,
        request: Request,
        question: str,
        user_id: str,
        conversation_id: str,
        extend_params: dict = None
    ) -> AsyncIterator[Any]:
        """
        异步生成流式响应数据块，并通过 Transmitter 发送。
        
        此方法封装了完整的流式响应生命周期，包括：
        - 启动会话
        - 调用子类实现的内容生成逻辑
        - 构建并发送消息包
        - 错误处理与回退机制
        - 结束会话
        
        Args:
            transmitter (Transmitter): 消息传输器实例，用于构建并发送消息包。
            request (Request): FastAPI 请求对象，包含请求上下文信息。
            question (str): 用户输入的问题文本内容。
            user_id (str): 当前用户的唯一标识符。
            conversation_id (str): 当前会话的唯一标识符，若为空则创建新会话。
            
        Yields:
            AsyncIterator[Any]: 异步生成的消息数据块。每个 yield 返回一个字典，格式如下：
                {
                    "data": str | Any,       # 需要发送的数据内容
                    "package_type": int,     # 消息类型标识（如：0 表示普通文本）
                    "is_last": bool          # 是否为最后一个数据块
                }
                
        Raises:
            Exception: 在执行过程中若发生内部异常，将被捕获并记录日志，同时向客户端发送错误消息。
        """
        try:
             self.transmitter = transmitter
             yield transmitter.start()
             session_id = self._generate_session_id(user_id, conversation_id)
             logger.info(f"会话开始：{session_id}")

             first_chunk = True 
             async for content in self._generate_content(
                request, 
                question, 
                user_id,
                conversation_id,
                extend_params=extend_params or {}
                ):
                   is_new = content.get("is_new_package") or first_chunk
                   data = content["data"]
                   package_type = content["package_type"]
                   is_last = content["is_last"]
                   #logger.debug(f"发送 chunk: data={data}, package_type={package_type}, is_last={is_last},is_new={is_new}")
                   # 使用子类返回的 is_last 标记
                   yield transmitter.send_message(
                       data=data,
                       package_type=package_type,
                       is_last=is_last,
                       is_new_package=is_new
                   )
                   first_chunk = False

             yield await transmitter.end()
             logger.info(f"会话结束：{session_id}")

        except Exception as e:
            error_msg = f"系统异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            error_msg = "系统繁忙，请稍后再试。"
            traceback.print_exc()
            yield transmitter.send_message(
                data=error_msg,
                package_type=0,
                is_last=True,
                is_new_package=True
            )
            yield await transmitter.end()

    @abstractmethod
    async def _generate_content(self, request: Request, question: str, user_id: str, conversation_id: str, extend_params: dict) -> AsyncIterator[Any]:
        """
        【抽象方法】子类必须实现的消息内容生成逻辑。
        
        此方法应逐个产出需要发送的消息内容，通常为字符串或结构化字典。
        每个产出项应包含必要的元信息，例如：
            {
                "data": str, 
                "package_type": int, 
                "is_last": bool
            }
        
        Args:
            request (Request): FastAPI 请求对象。
            question (str): 用户输入的问题文本。
            user_id (str): 用户唯一标识符。
            conversation_id (str): 会话唯一标识符。
            
        Yields:
            AsyncIterator[Any]: 异步生成的消息内容片段。
        """
        pass

    def _generate_session_id(self, user_id: str, conversation_id: str) -> str:
        """
        生成当前会话的唯一标识符，默认使用 CommonUtils 工具生成。
        
        子类可根据业务需求重写此方法以实现自定义会话 ID 策略。
        
        Args:
            user_id (str): 用户唯一标识符。
            conversation_id (str): 会话唯一标识符。
            
        Returns:
            str: 生成的 session_id。
        """
        return CommonUtils.generate_session_id(user_id, self.agent_code, conversation_id)
    
    async def _generate_content_stream(self, content: str) -> AsyncGenerator[str, None]:
        for char in content:
            yield char
            await asyncio.sleep(0.1)  

    def _ensure_package(self, result, default_type=0):
        if isinstance(result, dict) and 'package_type' in result:
            return result
        return self._wrap_package(result, default_type)

    def _wrap_package(self, data, package_type):
        """包装数据包结构"""
        return {
            "data": data,
            "package_type": package_type
        }           