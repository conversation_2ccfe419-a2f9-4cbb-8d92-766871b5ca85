from datetime import datetime, timezone
from typing import Optional
from sqlmodel import Field, SQLModel, Column, DateTime

# ModelManagement 实体类
# <AUTHOR> silence_w

class ModelManagementBase(SQLModel):
    """ModelManagement 基础模型"""
    model_name: str = Field(nullable=False,description="模型名称")
    model_type: str = Field(nullable=False,description="模型类型")
    model_code: str = Field(unique=True, nullable=False,description="模型code")
    api_domain: str = Field(nullable=False,description="api 域名")
    api_key: Optional[str] = Field(default=None,description="api key")
    create_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="创建时间"
    )
    update_time: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="更新时间"
    )
    create_by: str = Field(description="创建者")
    update_by: Optional[str] = Field(description="修改者")

class ModelManagementTable(ModelManagementBase, table=True):
    """数据库中的 ModelManagement 模型"""
    __tablename__ = "model_management"
    id: int = Field(default=None, primary_key=True)


class ModelManagementCreate(ModelManagementBase):
    """创建 ModelManagement 模型"""


class ModelManagementUpdate(SQLModel):
    """更新 ModelManagement 模型"""
    model_name: Optional[str] = None
    model_type: Optional[str] = None
    api_domain: Optional[str] = None
    api_key: Optional[str] = None
    

class ModelManagementInDBBase(ModelManagementBase):
    """数据库中的 ModelManagement 模型"""
    id: int