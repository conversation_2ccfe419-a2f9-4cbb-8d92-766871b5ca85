import unittest
import asyncio
from datetime import datetime, timezone
from uuid import uuid4

from core.services.database import db_manager
from core.services.database.crud.conversation import conversation_curd
from core.services.database.schemas.conversation import ConversationCreate


class TestConversationBatchDelete(unittest.TestCase):
    """测试会话批量删除功能"""

    def setUp(self):
        """设置测试环境"""
        self.db_manager = db_manager
        self.test_user_id = 12345
        self.sample_conversations = []

    def tearDown(self):
        """清理测试环境"""
        # 清理测试数据
        asyncio.run(self._cleanup_test_data())

    async def _cleanup_test_data(self):
        """清理测试数据的异步实现"""
        async with self.db_manager.session() as session:
            for conversation in self.sample_conversations:
                try:
                    await conversation_curd.remove(
                        db=session, _id=conversation.id, hard_delete=True
                    )
                except Exception:
                    pass  # 忽略清理时的错误

    async def _create_test_conversations(self, count: int = 3):
        """创建测试会话数据"""
        async with self.db_manager.session() as session:
            for i in range(count):
                conversation_data = ConversationCreate(
                    id=uuid4().hex,
                    title=f"测试会话 {i+1}",
                    user_id=self.test_user_id,
                    current_agent_code="test_agent",
                    created_at=datetime.now(timezone.utc),
                )
                conversation = await conversation_curd.create(
                    db=session, obj_input=conversation_data
                )
                self.sample_conversations.append(conversation)

    def test_batch_soft_delete_success(self):
        """测试批量软删除成功"""
        asyncio.run(self._test_batch_soft_delete_success())

    async def _test_batch_soft_delete_success(self):
        """测试批量软删除成功的异步实现"""
        # 创建测试数据
        await self._create_test_conversations(3)
        
        conversation_ids = [conv.id for conv in self.sample_conversations]
        
        async with self.db_manager.session() as session:
            # 执行批量软删除
            result = await conversation_curd.batch_soft_delete(
                db=session,
                conversation_ids=conversation_ids,
                user_id=self.test_user_id
            )
            
            # 验证删除结果
            self.assertEqual(result["deleted_count"], 3)
            self.assertEqual(len(result["deleted_ids"]), 3)
            self.assertEqual(len(result["not_found_ids"]), 0)
            self.assertEqual(result["total_requested"], 3)
            
            # 验证会话确实被软删除
            for conv_id in conversation_ids:
                conversation_list = await conversation_curd.get_by_conversation_id(
                    session, _id=conv_id, include_deleted=False
                )
                self.assertEqual(len(conversation_list), 0)
                
                # 验证在包含已删除记录的查询中能找到
                conversation_list_with_deleted = await conversation_curd.get_by_conversation_id(
                    session, _id=conv_id, include_deleted=True
                )
                self.assertEqual(len(conversation_list_with_deleted), 1)
                self.assertTrue(conversation_list_with_deleted[0].is_deleted)

    def test_batch_soft_delete_partial_success(self):
        """测试批量软删除部分成功（部分会话不存在）"""
        asyncio.run(self._test_batch_soft_delete_partial_success())

    async def _test_batch_soft_delete_partial_success(self):
        """测试批量软删除部分成功的异步实现"""
        # 创建测试数据
        await self._create_test_conversations(2)
        
        existing_ids = [conv.id for conv in self.sample_conversations]
        non_existing_ids = [uuid4().hex, uuid4().hex]
        all_ids = existing_ids + non_existing_ids
        
        async with self.db_manager.session() as session:
            # 执行批量软删除
            result = await conversation_curd.batch_soft_delete(
                db=session,
                conversation_ids=all_ids,
                user_id=self.test_user_id
            )
            
            # 验证删除结果
            self.assertEqual(result["deleted_count"], 2)
            self.assertEqual(len(result["deleted_ids"]), 2)
            self.assertEqual(len(result["not_found_ids"]), 2)
            self.assertEqual(result["total_requested"], 4)
            
            # 验证存在的会话被删除
            for conv_id in existing_ids:
                self.assertIn(conv_id, result["deleted_ids"])
            
            # 验证不存在的会话在not_found_ids中
            for conv_id in non_existing_ids:
                self.assertIn(conv_id, result["not_found_ids"])

    def test_batch_soft_delete_wrong_user(self):
        """测试批量软删除错误用户的会话"""
        asyncio.run(self._test_batch_soft_delete_wrong_user())

    async def _test_batch_soft_delete_wrong_user(self):
        """测试批量软删除错误用户会话的异步实现"""
        # 创建测试数据
        await self._create_test_conversations(2)
        
        conversation_ids = [conv.id for conv in self.sample_conversations]
        wrong_user_id = 99999
        
        async with self.db_manager.session() as session:
            # 执行批量软删除（使用错误的用户ID）
            result = await conversation_curd.batch_soft_delete(
                db=session,
                conversation_ids=conversation_ids,
                user_id=wrong_user_id
            )
            
            # 验证删除结果 - 应该没有删除任何会话
            self.assertEqual(result["deleted_count"], 0)
            self.assertEqual(len(result["deleted_ids"]), 0)
            self.assertEqual(len(result["not_found_ids"]), 2)
            self.assertEqual(result["total_requested"], 2)
            
            # 验证会话仍然存在且未被删除
            for conv_id in conversation_ids:
                conversation_list = await conversation_curd.get_by_conversation_id(
                    session, _id=conv_id, include_deleted=False
                )
                self.assertEqual(len(conversation_list), 1)
                self.assertFalse(conversation_list[0].is_deleted)

    def test_batch_soft_delete_empty_list(self):
        """测试批量软删除空列表"""
        asyncio.run(self._test_batch_soft_delete_empty_list())

    async def _test_batch_soft_delete_empty_list(self):
        """测试批量软删除空列表的异步实现"""
        async with self.db_manager.session() as session:
            # 执行批量软删除（空列表）
            result = await conversation_curd.batch_soft_delete(
                db=session,
                conversation_ids=[],
                user_id=self.test_user_id
            )
            
            # 验证删除结果
            self.assertEqual(result["deleted_count"], 0)
            self.assertEqual(len(result["deleted_ids"]), 0)
            self.assertEqual(len(result["not_found_ids"]), 0)
            self.assertEqual(result["total_requested"], 0)


if __name__ == "__main__":
    unittest.main()
