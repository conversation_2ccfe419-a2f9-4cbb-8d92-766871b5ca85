from typing import Dict, Any, AsyncIterator, Optional
import json
from fastapi import Request
from llmclient.llm_client import LLMClient
from core.config.app_logger import logger
from core.base.base_sender import BaseSender
import re
from core.config.app_constant import AppConstant
from langchain_core.messages import HumanMessage
from core.langgraph.graph_builder import BaseGraphBuilder
import traceback
from langgraph.types import Command,StateSnapshot,Interrupt
from langchain_core.tools import tool
from core.config.app_config import config


# 基于LangGraph 发送器基类，用于封装流式响应生成逻辑。
# <AUTHOR> xiangjh

class LangGraphBaseSender(BaseSender):
    """
    基于LangGraph的发送器实现，提供统一接口处理与大模型交互后的消息发送流程，
    支持异步流式响应、会话管理、错误处理和日志记录等通用功能。

    Attributes:
        agent_code (str): 当前 Agent 的唯一标识符。
        _llm_client (LLMClient): LLM 客户端实例，延迟初始化。
    """

    def __init__(self, agent_code: str, recursion_limit: int = 10):
        """
        初始化 LangGraphBaseSender 实例。

        Args:
            agent_code (str): 当前 Agent 的唯一标识符，用于会话 ID 生成及日志记录。
            recursion_limit (int): 图执行的递归限制，默认为10
        """
        super().__init__(agent_code)
        self._llm_client = None
        self._workflow = None
        self._recursion_limit = recursion_limit

    async def _generate_content(self,
                               request: Request,
                               question: str,
                               user_id: str,
                               conversation_id: str,
                               extend_params: dict) -> AsyncIterator[Any]:
        """
        异步生成流式响应数据块。

        Args:
            request (Request): FastAPI 请求对象，用于获取上下文信息。
            question (str): 用户输入的问题文本。
            user_id (str): 用户唯一标识符。
            conversation_id (str): 会话唯一标识符。
            extend_params (dict): 扩展参数。

        Yields:
            AsyncIterator[Any]: 异步生成的消息数据块。
        """
        try:
            # 获取LLM客户端
            llm_client = await self._get_llm_client(request, user_id)

            # 从llm_client获取已注册的工具
            tools = llm_client.get_tools()
            logger.info(f"可用工具：{tools}")

            # 获取模型配置
            model_config = llm_client.get_model_config()

            # 构建最终问题
            final_question = self._build_final_question(question).strip()
            logger.info(f"最终问题：{final_question}")

            # 如果工作流定义尚未创建，则创建一次并缓存
            if not self._workflow:
                self._workflow = self.build_langgraph_agent(
                    agent_id=self.agent_code,
                    tools=tools,
                    model_config=model_config
                )

            # 生成会话ID
            session_id = self._generate_session_id(user_id, conversation_id)

            # 配置检查点
            config = {
                "recursion_limit": self._recursion_limit,
                "configurable": {
                    "thread_id": session_id
                }
            }

            # 从 Agent 的构建器获取检查点上下文管理器
            checkpointer_context = self.get_checkpointer()
            async with checkpointer_context as checkpointer:
                
                # 使用当前请求的检查点实例来编译图
                graph = self._workflow.compile(checkpointer=checkpointer)

                # 首先检查是否存在中断状态（使用异步接口）
                current_state = await graph.aget_state(config)
                logger.warning(f"当前状态：{current_state}")

                is_interrupted = self.check_interrupt_status(current_state)["is_interrupted"]
                if is_interrupted:
                    logger.info(f"human resume：{is_interrupted}")
                    action = extend_params.get("action") if extend_params else None
                    data = extend_params.get("data") if extend_params else None

                    if action is None:
                        if final_question == "no":
                            action = "no"
                        else:
                            action = "yes"
                        data = final_question
                        human_review = {"action": action, "data": data}

                    elif data:
                        human_review = {"action": action, "data": data}

                    async for event in graph.astream_events(Command(resume=human_review), config=config, version="v2", stream_mode="updates"):
                        result = self._process_chunk(event)
                        if result and result.get("data"):
                            yield {
                                "data": result.get("data", ""),
                                "is_last": False,
                                "package_type": result.get("package_type", 0),
                                "is_new_package": False
                            }

                else:
                    inputs = {
                        "messages": [HumanMessage(content=final_question)]
                    }

                    # 使用 astream_events 方式执行图，监听事件
                    async for event in graph.astream_events(inputs, config=config, version="v2", stream_mode="updates"):
                        result = self._process_chunk(event)
                        if result and result.get("data"):
                            yield {
                                "data": result.get("data", ""),
                                "is_last": False,
                                "package_type": result.get("package_type", 0),
                                "is_new_package": False
                            }

            logger.info("Sending last message")
            yield {
                "data": "",
                "is_last": True,
                "package_type": 0,
                "is_new_package": False
            }

        except Exception as e:
            error_msg = f"内部服务异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            traceback.print_exc()
            yield {
                "data": f"[ERROR] {error_msg}",
                "is_last": True,
                "package_type": 0,
                "is_new_package": True
            }
        finally:
            await llm_client.close_mcp_clients()


    def build_langgraph_agent(self, agent_id: str, tools: list, model_config: dict):
        # 这个方法现在只负责调用构建器，返回一个未编译的工作流
        # 检查点的处理移至 _generate_content 方法中
        return BaseGraphBuilder.build_langgraph_agent(
            self,
            agent_id,
            tools,
            model_config
        )

    def _build_final_question(self, raw_question: str) -> str:
        """问题构建"""
        return raw_question.strip()
    
    def get_checkpointer(self):
        """根据配置返回不同的检查点实现"""
        agent_config = None
        for agent in config.agents:
            if agent.name == self.agent_code:
                agent_config = agent
                break
        
        # 如果没有找到特定配置，使用默认的内存检查点
        if agent_config is None or agent_config.checkpoint == "memory":
            from core.langgraph.shared_memory_checkpointer import get_shared_memory_checkpointer
            logger.debug("使用全局共享内存检查点(默认)")
            return get_shared_memory_checkpointer()
        elif agent_config.checkpoint == "redis":
            from langgraph.checkpoint.redis.aio import AsyncRedisSaver
            logger.debug(f"使用 Redis 检查点：{config.redis.url}")
            return AsyncRedisSaver.from_conn_string(config.redis.url)
        else:
            # 如果配置了其他值，回退到内存检查点
            from core.langgraph.shared_memory_checkpointer import get_shared_memory_checkpointer
            logger.warning(f"未知的检查点配置: {agent_config.checkpoint}，使用全局共享内存检查点(默认)")
            return get_shared_memory_checkpointer()

    def check_interrupt_status(self, current_state: StateSnapshot):
        """判断中断状态"""
        status = {"is_interrupted": False, "interrupt_values": []}
        interrupts_found = False

        if hasattr(current_state, 'interrupts') and current_state.interrupts:
            for interrupt in current_state.interrupts:
                if isinstance(interrupt, Interrupt): # 旧格式
                    status["interrupt_values"].append(interrupt.value)
                    interrupts_found = True
                elif isinstance(interrupt, dict) and 'value' in interrupt: # 新格式
                    status["interrupt_values"].append(interrupt['value'])
                    interrupts_found = True

        if hasattr(current_state, 'tasks') and current_state.tasks:
            for task in current_state.tasks:
                if hasattr(task, 'interrupts') and task.interrupts:
                    for interrupt in task.interrupts:
                        if isinstance(interrupt, dict) and 'value' in interrupt:
                            status["interrupt_values"].append(interrupt['value'])
                            interrupts_found = True

        status["is_interrupted"] = interrupts_found
        return status


    async def _get_llm_client(self, request: Request, user_id: str) -> LLMClient:
        """
        获取或初始化 LLM 客户端实例。

        Args:
            request (Request): FastAPI 请求对象。
            user_id (str): 用户唯一标识符。

        Returns:
            LLMClient: 已初始化的 LLM 客户端实例。
        """
        self._llm_client = LLMClient(
            request=request,
            user_id=user_id,
            agent_id=self.agent_code,
            agent_type="langgraph"  # 指定使用LangGraph
        )
        await self._llm_client.init_async()
        return self._llm_client

    def _process_chunk(self, chunk: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """按事件类型分发处理每个 chunk 事件。返回包含 data 和 package_type 的字典，或 None。"""
        event_type = chunk.get("event")
        if event_type == "on_tool_start":
            logger.debug(f"chunk: {chunk}")
            return self._handle_tool_start(chunk)
        elif event_type == "on_tool_end":
            logger.debug(f"chunk: {chunk}")
            return self._handle_tool_end(chunk)
        elif event_type == "on_chain_stream":
            return self._handle_chain_stream(chunk)
        elif event_type == "on_chat_model_stream":
            return self._handle_chat_model_stream(chunk)
        return None
    def _handle_chain_stream(self, chunk: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理 on_chain_stream 事件，包含中断的特殊处理。"""
        try:
            _chunk = chunk.get("data", {}).get("chunk")
            if not isinstance(_chunk, dict):
                return None
            if "__interrupt__" in _chunk:
                interrupt_tuple = _chunk["__interrupt__"]
                if interrupt_tuple:
                    logger.debug(f"interrupt_tuple: {interrupt_tuple}")
                    interrupt_obj = interrupt_tuple[0]
                    value_dict = getattr(interrupt_obj, "value", {}) or {}
                    message = value_dict.get("message")
                    options = value_dict.get("options")
                    text = f"{message}" if message else ""
                    if options:
                        text = f"{text}"+f"选项：{options}"
                    return self._wrap_package(text, AppConstant.DEFAULT_PACKAGE_TYPE)
            return None
        except Exception:
            logger.exception("处理 on_chain_stream 事件异常")
            return None

    def _handle_chat_model_stream(self, chunk: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理 on_chat_model_stream 事件，提取增量内容。"""
        try:
            data_obj = chunk.get("data", {})
            sub_chunk = data_obj.get("chunk")
            content = None
            if hasattr(sub_chunk, "content"):
                content = sub_chunk.content
            elif isinstance(sub_chunk, dict):
                content = sub_chunk.get("content")
            if content:
                logger.debug(f"chunk: {content}")
                return self._wrap_package(content, AppConstant.DEFAULT_PACKAGE_TYPE)
            return None
        except Exception:
            logger.error("处理 on_chat_model_stream 事件异常")
            return None


    def _handle_tool_end(self, chunk: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理 on_tool_end 事件，返回包含 data 和 package_type 的 dict。
        {'event': 'on_tool_end', 
         'data': {'output': [{'companycode': '000009', 'companyname': '深圳万科'}, {'companycode': '000010', 'companyname': '成都万科'}], 'input': {'company_name': '万科'}}, 
         'run_id': '7b69683e-eb8d-413e-9721-5cd5707dba2b', 
         'name': 'query_company_info', 
         'tags': ['seq:step:1'], 
         'metadata': {'thread_id': 'user:10668:agentcode:elephant-agent:861230a2322847f883c122ab6b5546e4', 
                      'langgraph_step': 3, 'langgraph_node': 'run_tool', 'langgraph_triggers': ('branch:to:run_tool',), 'langgraph_path': ('__pregel_pull', 'run_tool'), 'langgraph_checkpoint_ns': 'run_tool:c13d22b5-1486-57b7-d100-a2a90fddfed4', 'checkpoint_ns': 'run_tool:c13d22b5-1486-57b7-d100-a2a90fddfed4'}, 
         'parent_ids': ['b62811d5-42fc-4160-9cba-1a485ef6dd0f', 'b0637884-d7f8-4ab0-8f04-b01c084ab5a9']}

         {'event': 'on_tool_end', 
          'data': {'output': ToolMessage(
                                    content='[{"companycode": "000009", "companyname": "深圳万科"}, {"companycode": "000010", "companyname": "成都万科"}]', 
                                    name='query_company_info', tool_call_id='call_09ykom3ihf983vmnsvf9ba50'), 'input': {'company_name': '万科'}}, 'run_id': '7373d956-996e-4d71-917f-1474a9b2cdc2', 
          'name': 'query_company_info', 
          'tags': ['seq:step:1'], 
          'metadata': {'thread_id': 'user:10668:agentcode:elephant-agent:57d05caa60314c78b12a3292c78df6ba', 'langgraph_step': 2, 'langgraph_node': 'tools', 'langgraph_triggers': ('branch:to:tools',), 'langgraph_path': ('__pregel_pull', 'tools'), 'langgraph_checkpoint_ns': 'tools:24238fde-2ca4-6ab5-90ce-00c15f68e9da', 'checkpoint_ns': 'tools:24238fde-2ca4-6ab5-90ce-00c15f68e9da'}, 
          'parent_ids': ['11700ce6-439b-4d8d-b085-03b1fd81264d', '61470e3b-6a5f-44fd-997b-96802a94c986']}
         """
        try:
            # 兼容可能的字段位置
            data_dict = chunk.get("data", {}) if isinstance(chunk.get("data"), dict) else {}
            tool_name = chunk.get("name") or data_dict.get("name") or data_dict.get("tool")
            output = None
            inner_chunk = data_dict.get("chunk") if isinstance(data_dict.get("chunk"), dict) else None
            if inner_chunk and isinstance(inner_chunk, dict):
                output = inner_chunk.get("output")

            output = output or data_dict.get("output")
            if tool_name:
                tool_chunk = {"tool": tool_name, "output": output}
                xml = self._handle_tool_result(tool_chunk) or ""
                return xml
            return None
        except Exception:
            logger.error("处理 on_tool_end 事件异常", exc_info=True)
            return None

    def _handle_tool_start(self, chunk: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理 on_tool_start 事件，构造工具开始的状态 XML 并包装返回。"""
        try:
            data_dict = chunk.get("data", {}) if isinstance(chunk.get("data"), dict) else {}
            tool_name = chunk.get("name") or data_dict.get("name") or data_dict.get("tool")
            if not tool_name:
                return None
            status = "loading"
            tool_result = ""
            xml_data = f"""<message-embedded>
                                <state>
                                    <set>
                                        <strategy>replace</strategy>
                                        <path>{tool_name}</path>
                                        <value>{status}</value>
                                    </set>
                                    <set>
                                        <strategy>replace</strategy>
                                        <path>{tool_name}_result</path>
                                        <value>{tool_result}</value>
                                    </set>
                                </state>
                                <widget>
                                    <code>@BuildIn/Tool</code>
                                    <props>
                                        <name>{tool_name}</name>
                                        <status>{{{{state.{tool_name}}}}}</status>
                                        <result>{{{{state.{tool_name}_result}}}}</result>
                                    </props>
                                </widget>
                            </message-embedded>"""
            return self._wrap_package(xml_data, AppConstant.DEFAULT_PACKAGE_TYPE)
        except Exception:
            logger.exception("处理 on_tool_start 事件异常")
            return None

    def _handle_tool_result(self, chunk: Dict[str, Any]) -> str:
        """
        处理工具调用结果类型的 chunk。

        Args:
            chunk (Dict[str, Any]): 包含 tool 和 output 的数据块。

        Returns:
            str: XML 格式的工具调用结果表示。
        """
        """
        处理工具调用结果类型的 chunk （可单独重写的子方法）。
        
        将原始输出解析为结构化数据，并生成对应的 XML 格式响应。
        
        Args:
            chunk (Dict[str, Any]): 包含 tool 和 output 的数据块。
            
        Returns:
            str: XML 格式的工具调用结果表示。
        """
        tool_name = chunk.get("tool")
        if not tool_name:
            return None
        
        tool_result = chunk.get("result", "")
        tool_msg = chunk.get("tool_msg", "")
        tool_output = chunk.get("output", "")
        
        status= "failed"
        if tool_result :
            status = "success" if tool_result == "success" else "failed"
        else:
            # 检查是否是 ToolMessage 类型
            if str(type(tool_output)) == "<class 'langchain_core.messages.tool.ToolMessage'>":
                logger.debug(f"检测到 ToolMessage 类型: {type(tool_output)}")
                content = getattr(tool_output, 'content', '')
                if content and content.strip():
                    status = "success"
                else:
                    status = "failed"
            elif isinstance(tool_output, str):
                text_match = re.search(r"text='({.*?})'", tool_output, re.DOTALL)
                if text_match:
                    try:
                        # 处理多层转义字符
                        text_content = (
                            text_match.group(1)
                            .replace("\\'", "'")
                            .replace('\\\\"', '"')
                            .replace('\\"', '"')
                        )
                        content_data = json.loads(text_content)
                        success = content_data.get('success', False)
                        status = "success" if success else "failed"

                    except Exception as e:
                        logger.error(f"最终解析失败: {str(e)}")
                        status = "failed"
            elif isinstance(tool_output, dict):
                logger.debug(f"tool_output: {tool_output}")
                success = tool_output.get('success', False)
                status = "success" if success else "failed"
        
        xml_data = f"""<message-embedded>
                            <state>
                                <set>
                                    <strategy>replace</strategy>
                                    <path>{tool_name}</path>
                                    <value>{status}</value>
                                </set>
                                <set>
                                    <strategy>replace</strategy>
                                    <path>{tool_name}_result</path>
                                    <value>{tool_msg}</value>
                                </set>
                            </state>
                        </message-embedded>"""
        return self._wrap_package(xml_data, AppConstant.DEFAULT_PACKAGE_TYPE)