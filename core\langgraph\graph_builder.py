from typing import List, Dict, Any, Annotated, Sequence, Optional, TypedDict, Literal
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.tools import BaseTool
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode, tools_condition, create_react_agent
from langchain_core.runnables import RunnableConfig
from core.config.app_config import ModelConfig
from core.config.app_logger import logger
from core.template.template_config import get_prompt_template
from abc import ABC, abstractmethod

from core.config.app_constant import AppConstant
from core.services.database.crud.agent_prompt import agent_prompt_crud

# 基于LangGraph 构建基类，用于封装流式响应生成逻辑。
# <AUTHOR> xiangjh

class AgentState(TypedDict):
    """Agent状态定义 - 通用基础状态

    Attributes:
        messages: 消息历史列表，使用add_messages函数进行累积
        intermediate_steps: 中间步骤列表
        agent_outcome: Agent执行结果
        tool_result: 存储工具调用结果
    """
    messages: Annotated[Sequence[BaseMessage], add_messages]
    intermediate_steps: List[tuple]
    agent_outcome: Any
    tool_result: Any



class BaseGraphBuilder(ABC):
    """LangGraph Agent基类

    该基类提供了构建基于LangGraph的Agent的通用功能，包括提示词模板管理、
    LLM实例构建、Agent节点创建等核心功能。子类需要实现build_langgraph_agent
    方法来定义具体的工作流构建逻辑。

    Attributes:
        PROMPT_TEMPLATES (dict): 默认提示词模板字典
    """

    PROMPT_TEMPLATES = {
        "default": """你是一个智能助手。\n请根据用户输入调用合适的工具，\n并严格按照指定格式返回结果。"""
    }

    def get_prompt_by_agent_id_and_node(self, agent_id: Optional[str], node_code: str) -> str:
        """根据Agent ID和节点名称获取对应的提示词模板

        Args:
            agent_id (Optional[str]): Agent的唯一标识符，如果为None则使用默认模板
            node_code (str): 节点名称，用于构建特定节点的提示词模板名称

        Returns:
            str: 格式化后的提示词模板字符串

        Raises:
            Exception: 当获取Agent或提示词模板过程中发生错误时记录警告日志并使用默认模板
        """
        from core.factory.agent_factory import AgentFactory
        logger.info(f">>>agent_id: {agent_id}, node_name: {node_code}")
        try:
            agent = AgentFactory.get_agent(agent_id)
            if agent_id and node_code is None:
                if hasattr(agent, 'prompt_text') and agent.prompt_text:
                    return agent.prompt_text
                else:
                    md_prompt_template = get_prompt_template()
                    return md_prompt_template.apply_prompt_template(prompt_name=agent_id)
            elif agent_id and node_code:
                prompt_list = agent_prompt_crud.get_by_agent_and_node_and_type_sync(
                    agent_code=agent_id,
                    node_code=node_code,
                    p_type=AppConstant.PROMPT_TYPE_SYSTEM,
                )
                if prompt_list and getattr(prompt_list[0], "prompt", None) and str(prompt_list[0].prompt).strip():
                    prompt_text = str(prompt_list[0].prompt).strip()
                    logger.debug(f"{agent_id} 节点：{node_code} 系统提示词: {prompt_text}")
                    return prompt_text

            else:
                md_prompt_template = get_prompt_template()
                prompt_name = f"{agent_id}_{node_code}"
                return md_prompt_template.apply_prompt_template(prompt_name=prompt_name)
        except Exception as e:
            logger.warning(f"获取Agent提示词失败: {str(e)}，使用默认模板")
        return self.PROMPT_TEMPLATES["default"]

    def build_prompt(self, agent_id: str,node_name: str) -> ChatPromptTemplate:
        """构建提示词模板

        Args:
            agent_id (str): Agent的唯一标识符

        Returns:
            ChatPromptTemplate: 构建好的聊天提示词模板对象
        """
        system_prompt = self.get_prompt_by_agent_id_and_node(agent_id, node_name)
        return ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="messages"),
        ])

    def build_llm(self, model_config: ModelConfig) -> ChatOpenAI:
        """构建LLM实例

        Args:
            model_config (ModelConfig): 模型配置对象，包含模型名称、API密钥等信息

        Returns:
            ChatOpenAI: 配置好的ChatOpenAI实例，支持流式输出
        """
        return ChatOpenAI(
            model=model_config.name,
            openai_api_key=model_config.api_key,
            openai_api_base=model_config.base_url,
            temperature=0,
            streaming=True
        )

    def create_agent_node(self, agent_id: str, node_name: str, tools: List[BaseTool],
                                model_config: ModelConfig, run_config: Optional[Dict[str, Any]] = None):
        """创建基于 LangGraph create_react_agent 的 Agent 节点，支持完整的 ReAct 循环

        使用 LangGraph 的 create_react_agent 创建支持工具调用的智能代理节点。
        该方法会自动处理工具调用、响应生成和状态管理。

        Args:
            agent_id (str): Agent的唯一标识符
            node_name (str): 节点名称
            tools (List[BaseTool]): 可用工具列表
            model_config (ModelConfig): 模型配置对象
            run_config (Optional[Dict[str, Any]]): 运行时配置，如run_name等

        Returns:
            function: 返回一个异步函数，该函数接收AgentState并返回处理结果
        """
        llm = self.build_llm(model_config)
        # 构建提示词模板（包含system与消息占位符），确保工具可被触发
        prompt_template = self.build_prompt(agent_id, node_name)

        logger.debug(f"创建 {node_name} 节点，工具数量: {len(tools)}, 工具名称: {[tool.name for tool in tools]}")
        try:
            react_agent = create_react_agent(model=llm, tools=tools, prompt=prompt_template)
        except Exception as e:
            logger.error(f"创建 ReAct Agent 失败: {str(e)}")
            raise

        if run_config:
            react_agent = react_agent.with_config(run_config)

        async def agent_node(state: AgentState) -> Dict[str, Any]:
            """Agent节点异步处理函数

            Args:
                state (AgentState): Agent当前状态，包含消息历史等信息

            Returns:
                Dict[str, Any]: 处理结果，包含消息和中间步骤
            """
            messages = state["messages"]
            logger.debug(f"调用节点{node_name}，\r\n messages: {messages},\r\n 使用工具: {tools}")

            # 使用 create_react_agent 处理消息，支持完整的 ReAct 循环
            response = await react_agent.ainvoke({"messages": messages})
            logger.debug(f"ReAct Agent Response: {response}")

            # 确保返回正确的消息格式
            if isinstance(response, dict) and "messages" in response:
                return {"messages": response["messages"]}
            else:
                # 兼容性处理：如果返回格式不符合预期，包装成消息列表
                logger.warning(f"ReAct Agent 返回格式异常: {type(response)}")
                return {"messages": [response] if response else []}

        return agent_node

    def create_agent_node_by_langchain(self, agent_id: str, node_name: str, tools: List[BaseTool],
                                model_config: ModelConfig, run_config: Optional[Dict[str, Any]] = None):
        """创建基于ReAct模式的Agent节点，支持自定义配置

        Args:
            agent_id (str): Agent的唯一标识符
            node_name (str): 节点名称
            tools (List[BaseTool]): 可用工具列表
            model_config (ModelConfig): 模型配置对象
            run_config (Optional[Dict[str, Any]]): 运行时配置，如run_name等

        Returns:
            function: 返回一个异步函数，该函数接收AgentState并返回处理结果
        """
        llm = self.build_llm(model_config)
        # 构建提示词模板（包含system与消息占位符），确保工具可被触发
        prompt_template = self.build_prompt(agent_id, node_name)

        # 确保工具正确绑定到 LLM
        logger.debug(f"创建 {node_name} 节点，工具数量: {len(tools)}, 工具名称: {[tool.name for tool in tools]}")

        # 使用与 CustomGraphBuilder 相同的成功模式：直接绑定工具到 LLM
        llm_with_tools = prompt_template | llm.bind_tools(tools)

        if run_config:
            llm_with_tools = llm_with_tools.with_config(run_config)

        async def agent_node(state: AgentState) -> Dict[str, Any]:
            """Agent节点异步处理函数

            Args:
                state (AgentState): Agent当前状态，包含消息历史等信息

            Returns:
                Dict[str, Any]: 处理结果，包含消息和中间步骤
            """
            messages = state["messages"]
            logger.debug(f"调用节点{node_name}，\r\n messages: {messages},\r\n 使用工具: {tools}")

            # 直接调用 LLM，获取响应（可能包含工具调用）
            response = await llm_with_tools.ainvoke({"messages": messages})
            logger.debug(f"LLM Response: {response}")

            return {"messages": [response]}

        return agent_node

    def create_tool_node(self, tools: List[BaseTool]):
        """创建工具节点

        Args:
            tools (List[BaseTool]): 工具列表

        Returns:
            ToolNode: 工具节点对象
        """
        return ToolNode(tools)


    @abstractmethod
    def build_langgraph_agent(self, agent_id: str, tools: List[BaseTool], model_config: ModelConfig):
        """构建LangGraph Agent的抽象方法，子类必须实现

        Args:
            agent_id (str): Agent的唯一标识符
            tools (List[BaseTool]): 可用工具列表
            model_config (ModelConfig): 模型配置对象
            checkpointer (Optional[Any]): 检查点后端，默认为 MemorySaver

        Returns:
            CompiledGraph: 编译好的LangGraph工作流对象
        """
        pass