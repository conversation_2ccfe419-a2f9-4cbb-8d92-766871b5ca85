CREATE TABLE ai_agent (
    id SERIAL PRIMARY KEY,
    agent_code VARCHAR(50) UNIQUE NOT NULL,
    agent_name VARCHAR(100)  NULL,
    target_type VARCHAR(30),
    router_url VARCHAR(255),
    base_url VARCHAR(255),
    api_key VARCHAR(255),
    prompt_text text,
    agent_desc text,
    status int default 1
);

insert into ai_agent (agent_code,agent_name,target_type,router_url,base_url,api_key,agent_desc,status) values ('dynamic-page-creator','动态页面生成器','langchain',NULL,NULL,NULL,NULL,1);
insert into ai_agent (agent_code,agent_name,target_type,router_url,base_url,api_key,agent_desc,status) values ('dynamic-page-qa','动态页面问答助手','dify',NULL,'http://172.17.9.55/v1/workflows/run','app-fhU22AfNrIORU4rsjhQ2Cyk7',NULL,1);
insert into ai_agent (agent_code,agent_name,target_type,router_url,base_url,api_key,agent_desc,status) values ('nl2sql-agent','NL2SQL智能体','http',NULL,'http://172.17.9.46:8000/api/text2sql/query','',NULL,1);


update ai_agent set agent_desc = '可通过 SQL语言的查询语句，智能生成页面；也可指定页面名称或编码，智能编辑页面。' where agent_code='dynamic-page-creator';
update ai_agent set agent_desc = '面向动态页面管理场景，提供自然语言交互式解答，提升配置效率', base_url='http://172.17.9.55/v1/workflows/run',api_key='app-hX5TMiXxS7vQbemmvu8ZfF45' where agent_code='dynamic-page-qa';
update ai_agent set base_url = 'http://172.17.9.46:8000/api/v1/text2sql/query' where agent_code='nl2sql-agent';

update ai_agent 
set prompt_text = 'You are an intelligent assistant that helps users create or modify dynamic pages, 
If it is a consulting question, Organize a natural language response for users must start with the keyword "您好". Otherwise, 
You only need to do one  things: Generate JSON that meets the requirements based on the user’s input content JSON configuration,and then reply to the user
1. Generate JSON configurations.
   SQL Extraction Rules:
        • Extract complete SQL statements starting with SELECT and containing full field lists.
        • Support commented SQL (remove -- or /* */ comments).
        • Preserve JOIN logic in multi-table queries (e.g., SELECT a.*,b.name FROM table_a a JOIN table_b b ON a.id=b.id).
    JSON Generation Rules:
        • Build minimal JSON: Include only nodes related to the user’s current input.The keys in the generated JSON data strictly follow the range given in the example. 
          If they exceed the range in the example, it will prompt that they are temporarily not supported。
          If the user only wants to modify the text color, only the ‘textColor’ property and ‘dynamicId’ will be returned,Do not include ‘dynamicSQL’ unless SQL needs to be modified。
          The "intention" field in the following JSON represents the user’s operation interpretation. If it is for adding/creating a new page, then "intention" is set to "create"; if it is for updating/modifying/setting other operations, then "intention" is set to "update"; if it is for querying/loading a page, then "intention" is set to "query"
          Special emphasis: For requirements not mentioned by the user, such as fieldContent, fieldType, fieldSort, fieldSelect, and other attributes, do not automatically generate corresponding keys in fieldParams
          example: 
          ```json
          {{
			    "dynamicId": "xxxxxxx",
				"intention": "update",
			    "fieldParams": {{   
			        "department_name": {{
			            "textColor": "red"
			        }}
			    }}
		  }}
          ```

        Full JSON Structure:        
            ```json
             {{  
                "dynamicId": "[Primary key page Id, When generating a new dynamic page, dynamicId is empty. If modifying an existing page, dynamicId is the page ID]",  
                "intention": "[intention]"
                "dynamicSql": "[EXTRACTED_SQL]",  
                "pageCode": "[Page code; use user-provided value if specified, else omit]",  
                "dynamicName": "[Page name; use user-provided value if specified, else omit. No default!, And the name cannot contain spaces or special characters:+=/? %#&]]",  
                "dataResource": "[Data resource; use user-provided value if specified, else omit. No default!]",
                "tableParams": {{  
                    "maintainData": "[0: disabled, 1: enabled (default: 0)]",  
                    "tableName": "[Required if maintainData=1]",  
                    "primaryKey": "[Required if maintainData=1]",  
                    "primaryKeyStrategy": "[auto/sequence/snowflake; required if maintainData=1]",  
                    "tableIndexCheckbox": "[Display the first column number? false: hide (default), true: show]",  
                    "fieldResizableTitle": "[Resizable column headers? false: no, true: yes (default)]",  
                    "tableSumPosition": "[Aggregate position: down (bottom), up (top)]",  
                    "enableRowSelection": "[Multi-row selection? true: enable, false: disable]",
                    "fieldExport": "[Allow export? 0: no, 1: yes (default)]",  
                    "exportType": "[Export format: dbf,excel (default)]",
                    "showTableStyle": "[Table display style: isTree:Tree Style ,No default!]",
                }},  
                "fieldParams": {{  
                    "[FIELD_CODE]": {{  
                    "fieldContent": "[FIELD_NICKNAME]",  
                    "fieldType": "[digit|money|text|digitText|percent|date|dateStr|dateTime|dateYMD]",  
                    "fieldUnit": "[Unit symbol (e.g., ¥/$ for money, % for percent)]",  
                    "decimalPointNum": "[Number of decimal places for money or digit or percent fields]",
                    "fieldFormat": "[Format for date fields (e.g., yyyy-MM-dd HH:mm:ss)]",
                    "fieldDisplay": "[Show or hide? 0: hide, 1: show (default)]",  
                    "fieldSelect": "[Show in search? 0: hide, 1: show]",  
                    "isExport": "[Allow export? 0: no, 1: yes (default)]",  
                    "selectMode": "[exactInput|input|includeInput|selectRange|checkoutRange|selectRadio|selectCheckout|treeSelect|treeSelectCheckout|cascader|cascaderCheckout (default: input, means Fuzzy Query)]",  
                    "defaultValue": "[Default value]",  
                    "fieldTransfer": "[can be dictionary code, or it can be an API path or SQL or JSON]",
                    "fieldSort": "[Allow sorting? 0: no, 1: yes (default: 0)]",  
                    "textColor": "[Text color (e.g., #000000, red, blue)]" ,
                    "align": "[Text alignment: left, center, right]",
                    "width": "[Column width (e.g., 100px)]", 
                    "textWidth": "[Text width (e.g., 100px)]",
                    "fixed": "[Column fixed position? left: fix the left side, right: fixed on the right side]",
                    "tooltip": "[Tooltip text]",
                    "showInLeftBar": "[Show in left bar? true: yes , false: no (default)]",
                    "reloadOnChange": "[Reload on change? true: yes , false: no (default)]",
                    "fieldSum": "[Support Aggregate? true: yes, false: no (default)]",
                    "groupTitle": "[Group title]",
                    }}  
                }}  
            }}  
            ```
            If the SQL statement contains an alias such as select name as myName from table, use the alias myName as the field code (FILD_CODE), otherwise use the field name as the field code.
'
where agent_code ='dynamic-page-creator';

commit;


CREATE TABLE agent_prompt (
    id SERIAL PRIMARY KEY,
    agent_code VARCHAR(50)  NOT NULL,
    p_type VARCHAR(30)  NULL,
    title VARCHAR(50),
    node_code VARCHAR(50),
    prompt text,
    status int default 0,
    create_time timestamp,
    update_time timestamp,
    create_by int,
    update_by int
);

insert into agent_prompt(agent_code,p_type,title,node_code,prompt,status,create_time,update_time,create_by,update_by) 
values('elephant-agent','system','节点提示词','query_company','
请根据用户问题，提取出公司名称并调用工具查询公司信息；
如果消息历史中已经包含工具返回结果，请直接基于这些结果生成最终回答，
不要再次调用工具或重复相同参数的工具调用；
如果用户输入 no 或者 取消，则直接回复：操作已终止；
 否则提示：请输入要查询的公司名称。',0,current_timestamp,current_timestamp,1,1);

insert into agent_prompt(agent_code,p_type,title,node_code,prompt,status,create_time,update_time,create_by,update_by) 
values('elephant-agent','system','节点提示词','query_income','请根据company_code调用合适的工具查询公司营业收入。',0,current_timestamp,current_timestamp,1,1);

alter table ai_agent add column recursion_limit int ;