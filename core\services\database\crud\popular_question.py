from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select,delete

from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.popular_question import (
    PopularQuestionTable,
    PopularQuestionCreate,
    PopularQuestionUpdate,
)


class CRUDPopularQuestion(CRUDBase[PopularQuestionTable, PopularQuestionCreate, PopularQuestionUpdate]):
    """热门问题CRUD操作实现"""

    async def get_by_agent_code(
        self, db: AsyncSession, *, agent_code: str, skip: int = 0, limit: int = 100
    ) -> List[PopularQuestionTable]:
        """根据智能体编码获取热门问题列表，按热度降序排序"""
        query = (
            select(PopularQuestionTable)
            .where(PopularQuestionTable.agent_code == agent_code)
            .where(PopularQuestionTable.disabled == "0")
            .order_by(PopularQuestionTable.sort)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_ordered_by_sort(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[PopularQuestionTable]:
        """获取所有热门问题，按热度降序排序"""
        query = (
            select(PopularQuestionTable)
            .order_by(PopularQuestionTable.sort)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def increment_heat(
        self, db: AsyncSession, *, question_id: int, increment: int = 1
    ) -> Optional[PopularQuestionTable]:
        """增加问题的热度值"""
        question = await self.get(db, question_id)
        if question:
            question.heat += increment
            db.add(question)
            await db.commit()
            await db.refresh(question)
        return question

    async def get_top_questions_by_agent(
        self, db: AsyncSession, *, agent_code: str, limit: int = 10
    ) -> List[PopularQuestionTable]:
        """获取指定智能体的热门问题TOP N"""
        query = (
            select(PopularQuestionTable)
            .where(PopularQuestionTable.agent_code == agent_code)
            .order_by(PopularQuestionTable.heat.desc())
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()
    
    async def delete_question_by_agent_code(
        self, db: AsyncSession, agent_code: str
    ) -> bool:
        """根据agente_code删除推荐问题"""
        stmt = delete(PopularQuestionTable).where(PopularQuestionTable.agent_code == agent_code)
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount > 0
    
    


popular_question_crud = CRUDPopularQuestion(PopularQuestionTable)
