from typing import List
from sqlmodel import select

from core.services.database import db_manager
from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.ai_agent import AIAgentTable, AIAgentCreate, AIAgentUpdate
from core.auth.security import filter_by_agent_permission

# AIAgent 增删改查操作
# <AUTHOR> xiangjh
class CRUDAIAgent(CRUDBase[AIAgentTable, AIAgentCreate, AIAgentUpdate]):
    """AI Agent CRUD操作实现"""
    
    @filter_by_agent_permission()
    async def query_agents(self,*,user=None) -> List[AIAgentTable]:
        """获取所有AI Agent列表"""
        async with db_manager.session() as db:
            result = await db.execute(select(AIAgentTable).where(AIAgentTable.disabled == "0"))
            return result.scalars().all()

    async def get_agent_by_code(self,  agent_code: str):
        """根据agent_code获取单个agent"""
        stmt = select(AIAgentTable).where(AIAgentTable.agent_code == agent_code)
        async with db_manager.session() as db:
           result = await db.execute(stmt)
           return result.scalar_one_or_none()  
          
    async def get_agent_by_id(self,  id: int):
        """根据agent_code获取单个agent"""
        stmt = select(AIAgentTable).where(AIAgentTable.id == id)
        async with db_manager.session() as db:
           result = await db.execute(stmt)
           return result.scalar_one_or_none()    
        
    async def query_all_agents(self) -> List[AIAgentTable]:
        """获取所有AI Agent列表"""
        async with db_manager.session() as db:
            result = await db.execute(select(AIAgentTable))
            return result.scalars().all()    


ai_agent_crud = CRUDAIAgent(AIAgentTable)