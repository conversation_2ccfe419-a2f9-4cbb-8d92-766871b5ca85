"""ai_agent删除status字段

Revision ID: 6d42f6a9a9fe
Revises: 2926ee678fb7
Create Date: 2025-07-22 18:30:47.366628

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '6d42f6a9a9fe'
down_revision: Union[str, None] = '2926ee678fb7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ai_agent', 'status')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ai_agent', sa.Column('status', sa.INTEGER(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
