from core.auth.adapter import AuthAdapter
from core.vo.user_vo import UserVO
from core.config.app_logger import logger

# 默认的认证适配器，用于解析用户数据
# <AUTHOR> xiangjh
class DefaultAuthAdapter(AuthAdapter):
    def parse_user_data(self, raw_data: dict) -> UserVO:
        # 自定义解析逻辑，例如：
        data = raw_data.get("data", {})
        user_id = data.get("userId", "")
        username = data.get("username", "unknown")
        permissions = data.get("permissions", [])
        
        permissions = data.get("permissions", [])
        if not isinstance(permissions, list):
            logger.warning("权限字段不是列表类型，默认赋空列表")
            permissions = []
        
        #logger.info(f"当前用户: {user_id}， 用户名: {username}，权限: {permissions}")

        curr_user = UserVO(
            userId=user_id,
            username=username,
            permissions=set(permissions),
            nickName=data.get("nickName")  
        )
        
        
        return curr_user