import traceback
from typing import Optional, AsyncGenerator, Union, List

from langchain_core.messages import ToolMessage
from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from fastapi import Request

from core.config.app_config import ModelConfig
from core.config.app_config import config
from core.services.model_config_service import ModelConfigService
from core.langchain.custom_runnable_with_history import RunnableWithCustomHistory
from core.langchain.agent_builder import build_agent_executor
from core.langchain.event_processor import process_agent_stream
from core.langchain.tool_registry import register_tools
from core.langchain.history_manager import HistoryManager
from core.config.app_logger import logger
from core.factory.mcp_factory import McpFactory
from mcpclient.mcp_client import McpClient
from utils.common_utils import CommonUtils
safe_json_dumps = CommonUtils.safe_json_dumps


# LLMClient 客户端封装类，用于封装大模型的调用
# @Author: xiangjh
class LLMClient:
    """大模型服务客户端中枢
    实现以下核心功能：
    - 多模型服务统一接入
    - 工具动态发现与注册
    - 智能体执行引擎构建
    - 流式对话生命周期管理
    """

    def __init__(
        self,
        request: Request,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        agent_type: str = "langchain"  # 新增参数，用于指定agent类型
    ):
        """初始化大模型服务客户端

        Args:
            request (Request): FastAPI 请求上下文，用于获取全局状态
            model_name (str): 模型标识符，优先级高于配置
            api_key (str): 模型API密钥，优先级高于配置
            base_url (str): 模型服务端点，优先级高于配置
            user_id (str): 用户全局唯一标识，用于会话隔离
            agent_id (str): 智能体标识，驱动业务逻辑适配
            agent_type (str): 智能体类型，可选值: "langchain"(默认), "langgraph"

        Raises:
            ValueError: 当必需参数缺失时抛出
        """
        # 直接从模块获取mcp_clients
        self.mcp_factory = McpFactory() 
        self.mcp_clients: Optional[list[McpClient]] = []
        self.model_config = config.model
        self.model_name = model_name or self.model_config.name
        self.api_key = api_key or self.model_config.api_key
        self.base_url = base_url or self.model_config.base_url
        self.user_id = user_id
        self.agent_id = agent_id
        self.agent_type = agent_type  # 保存agent类型
        self.tools = []
        self.history = None
        self.history_manager = HistoryManager(user_id=user_id, agent_id=agent_id)
        self.model = None
        self.chain_with_history = None
        self.agent_executor = None  # 用于LangChain

        if not self.model_name:
            raise ValueError("model_name 必须指定或在配置中定义")

    def build_llm(self, model_config: ModelConfig):
        """
        大模型引擎装配器
        --------------------------
        根据配置参数初始化大模型实例，支持以下特性：
        - 多后端兼容 (Azure OpenAI/OpenAI 兼容接口)
        - 流式响应支持
        - 温度系数调控

        Tech Spec:
            - Base Model: gpt-3.5-turbo-16k
            - Token Window: 16K tokens
            - API Version: 2023-05-15

        Args:
            model_config (ModelConfig):
                模型配置实体，包含认证信息及性能参数

        Returns:
            ChatOpenAI: 预配置的大模型驱动引擎
        """
        return ChatOpenAI(
            model=model_config.name,
            openai_api_key=model_config.api_key,
            openai_api_base=model_config.base_url,
            temperature=model_config.temperature,
            streaming=True,
        )

    async def init_mcp_clients(self):
        """
        初始化MCP客户端
        """
        self.mcp_clients = await self.mcp_factory.create_mcp_clients() 
        

    async def init_async(self):
        """异步初始化入口
        执行顺序：
        1. 根据 agent_id 动态获取模型配置
        2. 工具系统初始化：动态发现并注册可用工具
        3. 智能体引擎装配：构建端到端执行流水线
        """
        # 根据 agent_id 动态设置模型配置
        await self._init_model_config()

        self.model = self.build_llm(self.model_config)
        await self.init_mcp_clients()
        await self._init_tools()
        await self._init_agent()

    async def _init_model_config(self):
        """根据 agent_id 动态初始化模型配置"""
        try:
            # 使用独立的模型配置服务，避免循环依赖
            model_config = await ModelConfigService.get_model_config_for_agent(self.agent_id)

            # 更新模型配置
            self.model_config = model_config
            # 更新相关属性，保持参数优先级
            self.model_name = self.model_name or model_config.name
            self.api_key = self.api_key or model_config.api_key
            self.base_url = self.base_url or model_config.base_url

        except Exception as e:
            logger.error(f"初始化模型配置失败: {str(e)}，使用默认模型配置")

    async def _init_tools(self):
        """工具动态发现与注册
        实现流程：
        1. 从配置中心获取当前agent的排除工具列表
        2. 遍历MCP服务端点发现可用工具
        3. 应用排除规则过滤敏感/测试工具
        4. 注册合规工具到LangChain生态系统
        """
        self.tools = []
        current_agent = next(
            (_tmpA for _tmpA in config.agents if _tmpA.name == self.agent_id), None
        )
        exclude_tools = current_agent.exclude_tools if current_agent else []
        for mcp_client in self.mcp_clients:
            if mcp_client and mcp_client.session:
                try:
                    tools_response = await mcp_client.session.list_tools()
                    registered_tools = register_tools(
                        mcp_client, tools_response, exclude_tools
                    )
                    for tool_item in registered_tools:
                        self.tools.append(tool_item)
                except Exception as e:
                    logger.error(f"工具初始化失败: {str(e)}")
                    traceback.print_exc()

    def get_tools(self) -> List[BaseTool]:
        """获取已注册的工具列表
        
        Returns:
            List[BaseTool]: 已注册的工具列表
        """
        return self.tools
    
    def get_model_config(self) -> ModelConfig:
        """获取模型配置

        Returns:
            ModelConfig: 模型配置对象
        """
        return self.model_config

    def get_model_info(self) -> dict:
        """获取当前使用的模型信息

        Returns:
            dict: 包含模型详细信息的字典
        """
        return {
            "model_name": self.model_name,
            "model_key": getattr(self.model_config, 'model_key', 'unknown'),
            "base_url": self.base_url,
            "agent_id": self.agent_id,
            "temperature": self.model_config.temperature
        }

    async def _init_agent(self):
        """
        根据agent_type初始化相应的智能体执行引擎
        """
        if self.agent_type == "langchain":
            await self._init_langchain_agent()
        elif self.agent_type == "langgraph":
            logger.info("LangGraph agent initialized (graph building will be done in sender)")
        else:
            raise ValueError(f"不支持的agent_type: {self.agent_type}")

    async def _init_langchain_agent(self):
        """
        LangChain智能体执行引擎工厂
        --------------------------
        构建端到端的智能体执行流水线，集成以下核心组件：
        1. 业务规则提示词引擎
        2. 大模型计算核心
        3. 工具调用中间件
        ┌───────────────┐       ┌───────────────┐
        │ 提示词工程     │──────▶│ 大模型核心    │
        └───────────────┘       └───────────────┘
               ▲                       │
               │          ┌───────────▼───────────┐
               └──────────│ 工具中间件 & 历史管理  │
                          └───────────────────────┘
        Pipeline Workflow:
            1. 初始化提示词工程
            2. 装配大模型计算单元
            3. 创建工具感知型智能体
            4. 构建可观测执行器

        Args:
            agent_id (str): 智能体唯一标识符
            tools (List): 注册的工具集合，支持动态扩展
            model_config (ModelConfig): 大模型配置实体

        Returns:
            AgentExecutor: 可立即投入生产的智能体执行引擎

        Example:
            executor = build_agent_executor(
                agent_id="customer-service",
                tools=[search_tool, db_query_tool],
                model_config=prod_config
            )
            executor.invoke({"input": "用户查询请求"})
        """
        self.agent_executor = build_agent_executor(
            self.agent_id, self.tools, self.model
        )
        self.chain_with_history = RunnableWithCustomHistory(
            self.agent_executor,
            get_session_history=self.history_manager.get_redis_history,
            input_messages_key="input",
            history_messages_key="chat_history",
        )
    
    async def close_mcp_clients(self):
        """安全关闭所有MCP客户端连接"""
        #logger.warning("开始关闭所有MCP客户端连接")
        try:
            if self.mcp_factory:
                await self.mcp_factory.close_clients()
        except Exception as e:
            logger.error(f"关闭MCP客户端时出错: {str(e)}")

    ############################
    # 大模型流式调用入口
    ############################
    async def chat_stream(
        self, question: str, session_id: str = "default"
    ) -> AsyncGenerator[Union[str, dict], None]:
        """流式对话服务入口
        实现特性：
        - 上下文感知的对话历史管理
        - 实时事件流处理
        - 异常熔断机制
        - 资源自动回收

        Args:
            question (str): 用户输入的自然语言查询
            session_id (str): 会话唯一标识，格式：user_id:agent_id:timestamp

        Yields:
            Union[str, dict]: 标准化事件流，包含以下类型：
                - stream: 文本流片段
                - tool_result: 工具调用结果
                - error: 异常信息

        典型调用流程：
            1. 加载历史上下文
            2. 构建执行上下文
            3. 流式处理引擎执行
            4. 事件格式化输出
        """
        try:
            history = self.history_manager.get_redis_history(session_id)
            # 获取完整历史并过滤掉 ToolMessage
            raw_messages = await history.aget_messages()
            chat_history = [
                msg for msg in raw_messages if not isinstance(msg, ToolMessage)
            ]

            input_context = {"input": question, "chat_history": chat_history}

            config = {
                "configurable": {
                    "session_id": session_id,
                    "user_id": self.user_id,
                    "agent_id": self.agent_id,
                }
            }

            async for chunk in process_agent_stream(
                self.chain_with_history,
                input_context,
                config,
                history,
                question,
                session_id,
            ):
                #logger.debug(f"响应: {chunk}")
                yield chunk

        except Exception as e:
            logger.error(f"流式处理异常: {str(e)}")
            yield safe_json_dumps({
                "type": "error",
                "message": "对话处理中断，请稍后再试",
            })
            traceback.print_exc()

        finally:
            yield safe_json_dumps({"type": "stream", "content": " ", "package_type": 0})
            logger.info(">>>>>>流式响应结束<<<<<<<<<<<<")
