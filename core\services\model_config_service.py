from typing import Optional, Dict
from core.config.app_config import config, ModelConfig
from core.config.app_logger import logger
from core.services.database.crud.ai_agent_dao import ai_agent_crud

# 模型配置服务
# @Author: xiangjh
class ModelConfigService:
    """模型配置服务
    提供独立的模型配置获取功能
    支持二级缓存机制，提高性能
    """

    # 类级别的缓存，存储 agent_id -> ModelConfig 的映射
    _agent_model_cache: Dict[str, Optional[ModelConfig]] = {}
    _cache_initialized: bool = False

    @classmethod
    async def initialize(cls):
        """初始化缓存，从数据库加载所有 agent 的模型配置

        这个方法应该在应用启动时调用，类似于 AgentFactory.load_agents()
        会自动清空现有缓存并重新加载
        """
        try:
            logger.info("初始化 Agent 模型配置缓存...")
            cls.clear_cache()

            # 查询所有 agent
            agents = await ai_agent_crud.query_all_agents()

            for agent in agents:
                agent_id = agent.agent_code
                model_code = getattr(agent, 'model_code', None)

                if model_code:
                    # 根据 model_code 获取模型配置
                    model_config = config.get_model_by_key(model_code)
                    if model_config:
                        cls._agent_model_cache[agent_id] = model_config
                        logger.debug(f"缓存 Agent {agent_id} 的模型配置: {model_config.name} (key: {model_code})")
                    else:
                        cls._agent_model_cache[agent_id] = None
                        logger.warning(f"Agent {agent_id} 的 model_code={model_code} 未找到对应的模型配置")
                else:
                    # 没有配置 model_code，缓存 None
                    cls._agent_model_cache[agent_id] = None
                    logger.debug(f"Agent {agent_id} 未配置 model_code")

            cls._cache_initialized = True
            logger.info(f"Agent 模型配置缓存初始化完成，共缓存 {len(cls._agent_model_cache)} 个 Agent")

        except Exception as e:
            logger.error(f"初始化 Agent 模型配置缓存失败: {str(e)}")
            cls._cache_initialized = False

    @classmethod
    def refresh_agent_cache(cls, agent_id: str, model_code: Optional[str] = None):
        """刷新指定 agent 的缓存

        Args:
            agent_id (str): Agent ID
            model_code (Optional[str]): 新的模型代码，如果为 None 则清除缓存
        """
        if model_code:
            model_config = config.get_model_by_key(model_code)
            cls._agent_model_cache[agent_id] = model_config
            if model_config:
                logger.info(f"刷新 Agent {agent_id} 的模型配置缓存: {model_config.name} (key: {model_code})")
            else:
                logger.warning(f"刷新 Agent {agent_id} 缓存失败: model_code={model_code} 未找到对应配置")
        else:
            cls._agent_model_cache[agent_id] = None
            logger.info(f"清除 Agent {agent_id} 的模型配置缓存")

    @classmethod
    def clear_cache(cls):
        """清空所有缓存"""
        cls._agent_model_cache.clear()
        cls._cache_initialized = False
        logger.info("已清空 Agent 模型配置缓存")

    @classmethod
    async def get_model_config_by_agent_id(cls, agent_id: str) -> Optional[ModelConfig]:
        """根据 agent_id 获取模型配置（优先使用缓存）

        Args:
            agent_id (str): Agent 的唯一标识符

        Returns:
            Optional[ModelConfig]: 找到的模型配置，如果未找到则返回 None
        """
        if not agent_id:
            logger.debug("未指定 agent_id，返回 None")
            return None

        # 优先从缓存获取
        if cls._cache_initialized and agent_id in cls._agent_model_cache:
            cached_config = cls._agent_model_cache[agent_id]
            if cached_config:
                logger.debug(f"从缓存获取 Agent {agent_id} 的模型配置: {cached_config.name}")
            else:
                logger.debug(f"从缓存确认 Agent {agent_id} 未配置模型")
            return cached_config

        # 缓存未初始化或缓存中没有，从数据库查询
        logger.debug(f"缓存中未找到 Agent {agent_id}，从数据库查询")
        try:
            # 直接从数据库查询 agent 信息
            agent = await ai_agent_crud.get_agent_by_code(agent_id)
            if not agent:
                logger.debug(f"未找到 agent_id={agent_id} 对应的 agent")
                # 缓存查询结果
                cls._agent_model_cache[agent_id] = None
                return None

            # 检查 agent 对象中的 model_code 字段
            model_code = getattr(agent, 'model_code', None)
            if not model_code:
                logger.debug(f"Agent {agent_id} 未配置 model_code")
                # 缓存查询结果
                cls._agent_model_cache[agent_id] = None
                return None

            # 根据 model_code 从 AppConfig 中获取模型配置
            model_config = config.get_model_by_key(model_code)
            if model_config:
                logger.info(f"Agent {agent_id} 使用模型配置: {model_config.name} (key: {model_code})")
                # 缓存查询结果
                cls._agent_model_cache[agent_id] = model_config
                return model_config
            else:
                logger.warning(f"未找到 model_code={model_code} 对应的模型配置")
                # 缓存查询结果
                cls._agent_model_cache[agent_id] = None
                return None

        except Exception as e:
            logger.error(f"获取 Agent {agent_id} 的模型配置失败: {str(e)}")
            # 缓存失败结果
            cls._agent_model_cache[agent_id] = None
            return None

    @staticmethod
    def get_default_model_config() -> ModelConfig:
        """获取默认模型配置

        Returns:
            ModelConfig: 默认模型配置
        """
        return config.model

    @classmethod
    async def get_model_config_for_agent(cls, agent_id: str) -> ModelConfig:
        """为指定 agent 获取模型配置，如果没有则返回默认配置

        Args:
            agent_id (str): Agent 的唯一标识符

        Returns:
            ModelConfig: 模型配置（agent 专用或默认）
        """
        # 尝试获取 agent 专用的模型配置
        agent_model_config = await cls.get_model_config_by_agent_id(agent_id)

        if agent_model_config:
            return agent_model_config
        else:
            # 如果没有找到，返回默认配置
            logger.debug(f"Agent {agent_id} 使用默认模型配置")
            return cls.get_default_model_config()

    @classmethod
    def get_cache_info(cls) -> dict:
        """获取缓存状态信息

        Returns:
            dict: 缓存状态信息
        """
        return {
            "cache_initialized": cls._cache_initialized,
            "cached_agents_count": len(cls._agent_model_cache),
            "cached_agents": list(cls._agent_model_cache.keys()),
            "agents_with_models": [
                agent_id for agent_id, model_config in cls._agent_model_cache.items()
                if model_config is not None
            ]
        }
