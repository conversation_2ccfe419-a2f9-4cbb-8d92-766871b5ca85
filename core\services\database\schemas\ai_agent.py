from datetime import datetime, timezone
from typing import Optional
from sqlmodel import Field, SQLModel, Column, DateTime

# AIAgent 实体类
# <AUTHOR> xiangjh

class AIAgentBase(SQLModel):
    """AI Agent 基础模型"""
    agent_code: str = Field(unique=True, nullable=False,description="Agent 编码")
    agent_name: str = Field(nullable=False,description="Agent 名称")
    target_type: str = Field(default=None,description="连接目标对象")
    icon: Optional[str] = Field(default=None,description="图标:文件id")
    base_url: Optional[str] = Field(default=None,description="访问大模型的api地址")
    api_key: Optional[str] = Field(default=None,description="访问大模型的api key")
    router_url: Optional[str] = Field(default=None, description="路由URL")  
    prompt_text: Optional[str] = Field(default=None,description="提示词文本")
    model_code: Optional[str] = Field(default=None,description="模型编码")
    agent_desc: Optional[str] = Field(default=None,description="智能体描述信息")
    welcome_statement: Optional[str] = Field(default=None,description="欢迎文案")
    classify: Optional[str] = Field(default=None,description="分类")
    sort: int = Field(default=0, description="排序")
    disabled: str = Field(default='0', description="是否启用 0启用 1不启用")
    recursion_limit: Optional[int] = Field(default=10, description="图执行的递归限制，默认为10")
    create_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="创建时间"
    )
    update_time: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="更新时间"
    )
    create_by: str = Field(description="创建者")
    update_by: Optional[str] = Field(description="修改者")

class AIAgentTable(AIAgentBase, table=True):
    """数据库中的 AI Agent 模型"""
    __tablename__ = "ai_agent"
    id: int = Field(default=None, primary_key=True)


class AIAgentCreate(AIAgentBase):
    """创建 AI Agent 模型"""


class AIAgentUpdate(SQLModel):
    """更新 AI Agent 模型"""
    agent_code: Optional[str] = None
    agent_name: Optional[str] = None
    target_type: Optional[str] = None
    base_url: Optional[str] = None
    api_key: Optional[str] = None
    router_url: Optional[str] = None
    prompt_text: Optional[str] = None
    agent_desc: Optional[str] = None
    disabled: Optional[str] = None
    classify: Optional[str] = None
    recursion_limit: Optional[int] = None

class AIAgentInDBBase(AIAgentBase):
    """数据库中的 AI Agent 模型"""
    id: int