from core.base.langgraph_sender import LangGraphBaseSender
from core.langgraph.graph_builder_elephant import ElephantGraphBuilder
from typing import List
from langchain_core.tools import BaseTool
from core.config.app_config import ModelConfig
from core.config.app_logger import logger
from core.config.app_config import config
################################
# Elephant智能体
################################

# @Author: xiangjh
class ElephantSender(LangGraphBaseSender):
    """Elephant智能体"""

    def __init__(self, agent_code: str, recursion_limit: int = 20):
        """
        初始化 ElephantSender 实例。

        Args:
            agent_code (str): Agent 的唯一标识符
            recursion_limit (int): 图执行的递归限制，默认为20（适合企业查询场景）
        """
        super().__init__(agent_code, recursion_limit)
    
    def _build_final_question(self, raw_question: str) -> str:
        return raw_question
    
    
    def build_langgraph_agent(self, agent_id: str, tools: List[BaseTool], model_config: ModelConfig):
        """自定义构建LangGraph Agent的逻辑"""
        # 这里可以添加自定义的业务流程定义
        # 例如添加额外的节点或修改默认的图结构
        logger.info("自定义构建LangGraph Agent的逻辑")
        return  ElephantGraphBuilder().build_langgraph_agent(agent_id, tools, model_config)

