
from core.auth.security import check_user
from core.utils.file import getFilesUrl
from core.vo.user_vo import User<PERSON>
from fastapi import APIRouter, Depends, HTTPException, Header, Body
from typing import Annotated
from core.services.database import db_manager
from core.services.database.crud.ai_agent_dao import ai_agent_crud
from core.services.database.crud.model_management import model_management_crud
from core.services.database.crud.prompt_template import prompt_template_crud
from core.services.database.crud.popular_question import popular_question_crud
from core.config.app_logger import logger
from core.factory.agent_factory import AgentFactory
from core.config.app_config import config, ModelConfig
from core.services.model_config_service import ModelConfigService
from pydantic import BaseModel

router = APIRouter(prefix="/agent")

# 智能体查询

@router.get("")
async def query_agents(
    authorization: Annotated[str | None, Header()] = None,
    user: UserVO = Depends(check_user),
):
    """
    查询所有agent列表

    Returns:
        agent列表
    """

    try:
        agents = await ai_agent_crud.query_agents(user=user)
        agentIconIds = [agent.icon for agent in agents]

        # 首先获取所有agent数据中的图标地址
        for agent in agents:
            logger.debug(f"agent name: {agent.agent_name},code: {agent.agent_code}")
            if agent.icon:
                agentIconIds.append(agent.icon)
        # 调用span后端图片预览地址
        if agentIconIds:
            urls = getFilesUrl(agentIconIds, authorization, logger)
            for agent in agents:
                if agent.icon:
                    agent.icon = urls[agent.icon]

        return {
            "code": 200,
            "data": agents,
            "message": "查询成功",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e

@router.get("/refresh/{agent_code}")
async def refresh_agent(
    agent_code: str,
    user: UserVO = Depends(check_user) 
):
    """手动刷新指定agent缓存
    Args:
        agent_code: 要刷新的agent代码
    Returns:
        刷新结果
    """
    try:
        success = await AgentFactory.refresh_agent(agent_code)
        if not success:
            raise HTTPException(status_code=404, detail="Agent不存在或刷新失败")

        # 刷新 Agent 后，立即刷新对应的模型配置缓存
        try:
            agent = AgentFactory.get_agent(agent_code)
            model_code = getattr(agent, 'model_code', None)
            ModelConfigService.refresh_agent_cache(agent_code, model_code)
            logger.info(f"Agent {agent_code} 的模型配置缓存已同步刷新")
        except Exception as e:
            logger.error(f"刷新 Agent {agent_code} 的模型配置缓存失败: {str(e)}")
            # 即使模型配置缓存刷新失败，也不影响 Agent 刷新的成功状态

        return {
            "code": 200,
            "message": f"Agent {agent_code} 刷新成功",
            "data": {
                "agent_code": agent_code,
            }
        }
    except Exception as e:
        logger.error(f"刷新agent失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"刷新agent失败: {str(e)}")

@router.post("/refresh_model")
async def refresh_model():
    """刷新模型配置

    Returns:
        刷新结果和更新的模型列表
    """
    try:
        # 查询数据库中的所有模型配置
        db_models = await model_management_crud.query_models()
        logger.info(f"从数据库查询到 {len(db_models)} 个模型配置")

        # 获取当前配置文件中的模型列表
        current_models = config.model_list.copy()
        updated_count = 0
        added_count = 0

        for db_model in db_models:
            model_config = ModelConfig(
                name=db_model.model_name,
                model_key=db_model.model_code, 
                api_key=db_model.api_key or "",
                base_url=db_model.api_domain,
                temperature=0.0  
            )

            existing_index = None
            for i, existing_model in enumerate(current_models):
                if existing_model.model_key == db_model.model_code:
                    existing_index = i
                    break

            if existing_index is not None:
                current_models[existing_index] = model_config
                updated_count += 1
                logger.info(f"更新模型配置: {db_model.model_name} (key: {db_model.model_code})")
            else:
                current_models.append(model_config)
                added_count += 1
                logger.info(f"添加新模型配置: {db_model.model_name} (key: {db_model.model_code})")

        # 更新全局配置中的模型列表
        config.model_list = current_models

        # 重新初始化 ModelConfigService 的缓存（会自动清空现有缓存）
        await ModelConfigService.initialize()

        logger.info(f"模型配置刷新完成: 更新 {updated_count} 个，新增 {added_count} 个")

        return {
            "code": 200,
            "message": "模型配置刷新成功",
            "data": {
                "total_models": len(current_models),
                "updated_count": updated_count,
                "added_count": added_count,
                "models": [{"name": model.name, "model_key": model.model_key, "base_url": model.base_url} for model in current_models]

            }
        }

    except Exception as e:
        logger.error(f"刷新模型配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"刷新模型配置失败: {str(e)}")

@router.get("/models")
async def get_models():
    """获取当前所有可用的模型配置

    Returns:
        当前配置中的所有模型列表
    """
    try:
        models_data = []
        for model in config.model_list:
            models_data.append({
                "name": model.name,
                "model_key": model.model_key,
                "base_url": model.base_url,
                "temperature": model.temperature,
                "has_api_key": bool(model.api_key)  
            })

        return {
            "code": 200,
            "message": "获取模型列表成功",
            "data": {
                "total_count": len(models_data),
                "default_model": {
                    "model_key": config.model.model_key,
                    "name": config.model.name,
                    "base_url": config.model.base_url
                },
                "models": models_data
            }
        }

    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")

    
@router.post("/deleteAssociatedInfo/{id}")
async def refresh_agent(
    id: int
    # user: UserVO = Depends(check_user) 
):
    """删除指定id下的智能体相关的模板和推荐问题
    Args:
        id: 智能体id
    Returns:
        删除结果
    """
    try:
        agent = await ai_agent_crud.get_agent_by_id(id)
        if not agent:
                logger.error(f"未找到agent: {id}")
                return False
        async with db_manager.session() as session:
            await prompt_template_crud.delete_template_by_agent_code(session,agent.agent_code)
            await popular_question_crud.delete_question_by_agent_code(session,agent.agent_code)
        
        return {
            "code": 200,
            "message": f"Agent {id} 删除成功",
            "data": {
                "id": id,
                }
        }
    except Exception as e:
        logger.error(f"删除agent额外数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除agent额外数据失败: {str(e)}")
    
class AuthReqBody(BaseModel):
    authCode: str
    clientType: str | None = None
    version: str | None = None

@router.post("/auth")
async def proxy_auth(
    body: AuthReqBody = Body(..., description="认证参数")
):

    auth_code = body.authCode
    logger.info(f"auth_code: {auth_code}")

    return {
        "success": True,
        "message": "认证成功",
        "credentials": {
            "augment_username": "elephant",
            "augment_password": "123456",

        }
    }

    