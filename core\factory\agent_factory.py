from typing import Dict, Any
from core.config.app_logger import logger
from core.services.database.crud.ai_agent_dao import ai_agent_crud
from core.base.langchain_sender import LangchainBaseSender  
from core.base.dify_sender import DifyBaseSender
from core.base.http_sender import HttpBaseSender
from core.base.langgraph_sender import LangGraphBaseSender  # 新增LangGraph支持
from importlib import import_module
# Agent工厂类类，用于动态加载agent
# @Author: xiangjh

class AgentFactory:
    """Agent工厂中枢

    实现以下核心功能：
    - 代理元数据动态加载
    - 多目标平台适配器注册
    - 运行时对象缓存管理（仅缓存类，不缓存实例）
    - 异常安全机制
    - 服务发现 & 路由控制
    - 用户级别数据隔离（每次调用创建新实例）
    """
    _sender_classes: Dict[str, Any] = {}
    _agents: Dict[str, Any] = {}

    @classmethod
    def register_agents(cls,agent_code: str = None):
       from core.config.app_config import config
       """ 动态注册配置文件中定义的agent实现类 """
       for  agent_cfg in config.agents:
            if not agent_cfg.impl:
                continue

            if agent_code and agent_cfg.name != agent_code:
                continue

            try:
                # 动态导入类
                module_path, class_name = agent_cfg.impl.rsplit('.', 1)
                module = import_module(module_path)
                sender_class = getattr(module, class_name)

                # 只注册类，不实例化
                cls.register_sender_class(agent_cfg.name, sender_class)
                logger.info(f"注册 sender 类: {agent_cfg.name} => {agent_cfg.impl}")

            except Exception as e:
                logger.error(f"注册 sender类失败 [{agent_cfg.name}]: {str(e)}", exc_info=True)

    @classmethod
    async def load_agents(cls):
        """数据库元数据加载器

        执行流程：
        1. 从持久化存储加载代理元数据
        2. 动态注册目标平台适配器类
        3. 构建代理对象缓存（不缓存sender实例）
        4. 异常代理自动熔断

        Raises:
            DatabaseError: 数据库查询失败时抛出
            ValidationError: 元数据校验失败时抛出
        """
        try:
            cls.register_agents()
            agents = await ai_agent_crud.query_all_agents()

            for agent in agents:
                try:
                    # 检查是否有预注册的sender类
                    if sender_class := cls._sender_classes.get(agent.agent_code):
                        logger.debug(f"使用预注册的发送器类: {agent.agent_code}")
                    else:
                        # 根据target_type获取默认sender类
                        sender_class = cls._get_sender_class_by_target(agent.target_type)
                        if sender_class:
                            cls.register_sender_class(agent.agent_code, sender_class)
                            logger.debug(f"注册默认发送器类: {agent.agent_code} => {sender_class.__name__}")

                    cls._agents[agent.agent_code] = agent
                    logger.info(f"成功注册 agent: {agent.agent_name}, target: {agent.target_type}")
                except ValueError as e:
                    logger.warning(f"跳过 agent '{agent.agent_name}': {str(e)}")

        except Exception as e:
            logger.error(f"加载 agents 失败: {str(e)}")
            raise

    @classmethod
    def get_sender(cls, agent_code: str) -> Any:
        """适配器路由控制器 - 每次调用都创建新实例，实现用户级别数据隔离
        Args:
            agent_code (str): 代理唯一标识符，格式：<业务域>.<功能模块>

        Returns:
            Any: 新创建的目标平台适配器实例

        Raises:
            ServiceNotFound: 当适配器未注册时抛出
        """
        # 获取sender类
        sender_class = cls._sender_classes.get(agent_code)
        if not sender_class:
            raise ValueError(f"未找到 agent_code='{agent_code}' 对应的 sender类")

        # 获取agent配置
        agent = cls._agents.get(agent_code)
        if not agent:
            raise ValueError(f"未找到 agent_code='{agent_code}' 对应的agent配置")

        logger.debug(f"创建新的sender实例: {agent_code}")

        # 根据sender类型创建新实例
        try:
            if issubclass(sender_class, LangGraphBaseSender):
                recursion_limit = getattr(agent, 'recursion_limit', 10) or 10
                sender = sender_class(agent.agent_code, recursion_limit)
                logger.debug(f"创建 LangGraph sender实例: {agent.agent_code}, recursion_limit: {recursion_limit}, 实例id: {id(sender)}")
            else:
                sender = sender_class(agent.agent_code)
                logger.debug(f"创建 sender实例: {agent.agent_code}, 实例id: {id(sender)}")

            return sender
        except Exception as e:
            logger.error(f"创建sender实例失败 [{agent_code}]: {str(e)}", exc_info=True)
            raise ValueError(f"创建sender实例失败: {str(e)}")

    @classmethod
    def get_agent(cls, agent_code: str) -> Any:
        """
        从_agents中获取指定的agent对象
        
        :param agent_code: Agent的唯一标识符
        :return: 对应的agent对象
        :raises ValueError: 如果未找到对应的agent
        """
        agent = cls._agents.get(agent_code)
        if not agent:
            raise ValueError(f"未找到 agent_code='{agent_code}' 对应的agent")
        return agent

    @classmethod
    def register_sender_class(cls, agent_code: str, sender_class: Any):
        """注册sender类（不是实例）"""
        cls._sender_classes[agent_code] = sender_class

    @classmethod
    def register_sender_classes(cls, sender_classes: Dict[str, Any]):
        """批量注册sender类"""
        cls._sender_classes.update(sender_classes)

    # 保持向后兼容的方法（已废弃，建议使用register_sender_class）
    @classmethod
    def register_sender(cls, agent_code: str, sender: Any):
        """废弃方法：注册sender实例，现在改为注册sender类"""
        if isinstance(sender, type):
            cls.register_sender_class(agent_code, sender)
        else:
            cls.register_sender_class(agent_code, sender.__class__)

    @classmethod
    def register_senders(cls, senders: Dict[str, Any]):
        """废弃方法：批量注册sender实例，现在改为注册sender类"""
        for agent_code, sender in senders.items():
            cls.register_sender(agent_code, sender)

    @classmethod
    def _get_sender_class_by_target(cls, target_type: str) -> Any:
        """根据目标平台类型获取对应的 Sender 类"""
        # 默认映射
        target_map = {
            "HTTP": HttpBaseSender,
            "DIFY": DifyBaseSender,
            "LANGCHAIN": LangchainBaseSender,
            "LANGGRAPH": LangGraphBaseSender,
        }
        return target_map.get(target_type.upper())

    # 保持向后兼容的方法（已废弃）
    @classmethod
    def _get_sender_by_target(cls, target_type: str, agent_code: str = None) -> Any:
        """废弃方法：根据目标平台类型获取对应的 Sender 类"""
        return cls._get_sender_class_by_target(target_type)

    
    
    @classmethod
    async def refresh_agent(cls, agent_code: str) -> bool:
        """刷新指定agent的缓存数据并重新加载
        Args:
            agent_code: 要刷新的agent ID
        Returns:
            bool: 是否刷新成功
        """
        try:
            # 清除缓存
            if agent_code in cls._agents:
                del cls._agents[agent_code]
            if agent_code in cls._sender_classes:
                del cls._sender_classes[agent_code]
            logger.info(f"已清除agent缓存: {agent_code}")

            # 重新加载该agent
            agent = await ai_agent_crud.get_agent_by_code(agent_code)
            if not agent:
                logger.error(f"未找到agent: {agent_code}")
                return False

            # 重新注册sender类
            cls.register_agents(agent_code)

            # 检查是否有预注册的sender类
            if sender_class := cls._sender_classes.get(agent.agent_code):
                logger.debug(f"使用预注册的发送器类: {agent.agent_code}")
            else:
                # 根据target_type获取默认sender类
                sender_class = cls._get_sender_class_by_target(agent.target_type)
                if sender_class:
                    cls.register_sender_class(agent.agent_code, sender_class)
                    logger.debug(f"注册默认发送器类: {agent.agent_code} => {sender_class.__name__}")

            cls._agents[agent_code] = agent
            logger.info(f"成功刷新agent: {agent_code}")
            return True

        except Exception as e:
            logger.error(f"刷新agent缓存失败: {str(e)}", exc_info=True)
            return False