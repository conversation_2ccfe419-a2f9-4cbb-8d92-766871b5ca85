from typing import AsyncGenerator, Optional
import os
from core.config.app_logger import logger
from contextlib import asynccontextmanager

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    create_async_engine,
    async_scoped_session,
    async_sessionmaker,
)

from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.orm.scoping import ScopedSession
from sqlmodel import SQLModel

from core.config.app_config import config
from asyncio import current_task

def create_sync_engine():
    db_url = config.database.url

    if not db_url:
        error_message = "Error: Database URL is not configured"
        print(f"Error: {error_message}")
        raise Exception(error_message)

    try:
        # Convert async URL to sync URL
        if "postgresql+asyncpg://" in db_url:
            sync_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
        else:
            sync_url = db_url
        return create_engine(
            sync_url,
            pool_size=50,
            max_overflow=10,
            pool_timeout=30,
            pool_pre_ping=True,
            pool_recycle=3600,
        )
    except Exception as e:
        error_message = f"Error creating database engine: {e}"
        print(error_message)
        raise e


class DatabaseManager:
    """数据库连接管理器"""

    _instance = None
    _sync_engine = create_sync_engine()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, db_url: Optional[str] = None):
        if self._initialized:
            return
        self.db_url = db_url or config.database.url
        self.engine = create_async_engine(
            self.db_url,
            echo=os.getenv("DB_ECHO", "false").lower() == "true",
            pool_pre_ping=True,
        )
        self.async_session_maker = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        self._initialized = True

    async def init_db(self):
        """初始化数据库表结构"""
        async with self.engine.begin() as conn:
            # 创建所有表
            await conn.run_sync(SQLModel.metadata.create_all)
            logger.info("Database tables initialized")

    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        return self.async_session_maker()

    @asynccontextmanager
    async def session(self) -> AsyncGenerator[AsyncSession, None]:
        """会话上下文管理器"""
        session = await self.get_session()
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()

    @asynccontextmanager
    async def scoped_session(self) -> AsyncGenerator[AsyncSession, None]:
        """会话上下文管理器"""
        session_factory = async_sessionmaker(
            bind=self.engine, autoflush=False, autocommit=False
        )
        scoped_session = async_scoped_session(session_factory, scopefunc=current_task)
        try:
            yield scoped_session
            await scoped_session.commit()
        except Exception as e:
            await scoped_session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await scoped_session.close()
            await scoped_session.remove()

    @classmethod
    def createScopedSession(cls) -> ScopedSession:
        _ScopedSession = scoped_session(sessionmaker(bind=DatabaseManager._sync_engine))
        return _ScopedSession
