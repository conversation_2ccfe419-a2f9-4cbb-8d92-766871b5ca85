
from langgraph.checkpoint.memory import MemorySaver
from typing import Any, Optional
import threading
from core.config.app_logger import logger

# 解决 MemorySaver 无法跨请求保持状态的问题
# <AUTHOR> xiangjh
class SharedMemoryCheckpointer:
    """全局共享的内存检查点管理器"""
    
    _instance: Optional['SharedMemoryCheckpointer'] = None
    _lock = threading.Lock()
    
    def __init__(self):
        if SharedMemoryCheckpointer._instance is not None:
            raise RuntimeError("SharedMemoryCheckpointer 是单例模式，请使用 get_instance() 获取实例")
        self._memory_saver = MemorySaver()
        logger.info("创建全局共享 MemorySaver 实例")
    
    @classmethod
    def get_instance(cls) -> 'SharedMemoryCheckpointer':
        """获取单例实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    @classmethod
    def reset_instance(cls):
        """重置单例实例（主要用于测试）"""
        with cls._lock:
            if cls._instance is not None:
                logger.info("重置全局共享 MemorySaver 实例")
                cls._instance = None
    
    def get_memory_saver(self) -> MemorySaver:
        """获取共享的 MemorySaver 实例"""
        return self._memory_saver
    
    async def __aenter__(self) -> MemorySaver:
        """异步上下文管理器入口"""
        return self._memory_saver
    
    async def __aexit__(self, exc_type: Any, exc_value: Any, traceback: Any) -> None:
        """异步上下文管理器出口"""
        # MemorySaver 不需要特殊的清理操作
        pass


def get_shared_memory_checkpointer() -> SharedMemoryCheckpointer:
    """获取全局共享的内存检查点管理器"""
    return SharedMemoryCheckpointer.get_instance()
