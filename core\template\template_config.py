
from core.template.md_template import MdTemplate
from core.config.app_config import config
import os

# 提供获取Markdown模板的功能，支持默认模板和自定义模板
# <AUTHOR> xiangjh
full_config = config.full_config
PROMPT_DIR = full_config.get('prompt_dir')

default_md_prompt_template = MdTemplate(PROMPT_DIR)
customized_md_prompt_template = dict()

def get_prompt_template(
    _path: str = None  
):
    """
    获取Markdown模板实例
    
    根据路径参数返回对应的模板实例:
    - 不传_path参数时返回默认模板
    - 传_path参数时返回对应的自定义模板
    
    Args:
        _path (str, optional): 自定义模板路径，相对于PROMPT_DIR的子路径
        
    Returns:
        MdTemplate: 模板实例对象
        
    Examples:
        >>> # 获取默认模板
        >>> template = get_prompt_template()
        >>> 
        >>> # 获取自定义模板
        >>> template = get_prompt_template("custom/path")
    """
    if _path:
        if customized_md_prompt_template.get(_path):
            return customized_md_prompt_template[_path]
        customized_dir = os.path.join(PROMPT_DIR, _path)
        if not os.path.exists(customized_dir):
            return MdTemplate(PROMPT_DIR, None)
        customized_md_prompt_template[_path] = MdTemplate(PROMPT_DIR, customized_dir)
        return customized_md_prompt_template[_path]
    
    return default_md_prompt_template
    